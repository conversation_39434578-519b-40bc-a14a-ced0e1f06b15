extends Control

@onready var card_grid = $VBoxContainer/ScrollContainer/CardGridVBox/CardGridCenter/CardGrid
@onready var faction_filter = $VBoxContainer/FilterContainer/FactionFilter
@onready var rarity_filter = $VBoxContainer/FilterContainer/RarityFilter
@onready var collection_info = $VBoxContainer/Header/CollectionInfo
@onready var set_filter = $VBoxContainer/FilterContainer/SetFilter
var preview_overlay: Control
var card_preview: Control

var card_display_scene = preload("res://scenes/CardDisplay.tscn")
var current_cards: Array[Card] = []
var preview_tween: Tween
var current_preview_card: Card

func _ready():
	# Initialize preview nodes manually
	preview_overlay = $PreviewOverlay
	card_preview = $PreviewOverlay/CardPreview
	
	# Force hide preview overlay on startup
	if preview_overlay:
		preview_overlay.visible = false
		preview_overlay.modulate = Color(1, 1, 1, 0)
	
	if card_preview:
		card_preview.visible = true  # Keep card preview visible within overlay
		card_preview.modulate = Color(1, 1, 1, 0)
		card_preview.scale = Vector2(1, 1)
		card_preview.rotation = 0
	
	# Use call_deferred to ensure hiding happens after all initialization
	call_deferred("force_hide_preview")
	
	setup_filters()
	load_card_collection()
	setup_debug_panel()

func force_hide_preview():
	# Additional safety check to ensure preview is hidden
	if preview_overlay:
		preview_overlay.visible = false
		preview_overlay.modulate = Color(1, 1, 1, 0)
		
		# Also clear any placeholder text that might be showing
		clear_preview_placeholder_data()

func clear_preview_placeholder_data():
	# Clear all placeholder text from the preview nodes
	if not card_preview:
		return
	
	var preview_name = card_preview.get_node("PreviewCard/CardInfo/NameContainer/CardName")
	var preview_faction = card_preview.get_node("PreviewCard/CardInfo/FactionContainer/FactionName")
	var preview_rarity = card_preview.get_node("PreviewCard/CardInfo/RarityContainer/RarityName")
	var preview_affinity = card_preview.get_node("PreviewCard/CardInfo/AffinityContainer/AffinityName")
	var preview_set = card_preview.get_node("PreviewCard/CardInfo/SetContainer/SetName")
	
	var preview_top = card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Top")
	var preview_right = card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Right")
	var preview_bottom = card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Bottom")
	var preview_left = card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Left")
	
	var preview_power_sum = card_preview.get_node("PreviewCard/CardInfo/StatsContainer/PowerSum")
	var preview_avg_power = card_preview.get_node("PreviewCard/CardInfo/StatsContainer/AvgPower")
	
	# Clear all text
	if preview_name: preview_name.text = ""
	if preview_faction: preview_faction.text = ""
	if preview_rarity: preview_rarity.text = ""
	if preview_affinity: preview_affinity.text = ""
	if preview_set: preview_set.text = ""
	
	if preview_top: preview_top.text = ""
	if preview_right: preview_right.text = ""
	if preview_bottom: preview_bottom.text = ""
	if preview_left: preview_left.text = ""
	
	if preview_power_sum: preview_power_sum.text = ""
	if preview_avg_power: preview_avg_power.text = ""



func _on_back_button_pressed():
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _on_close_button_pressed():
	hide_card_preview()

# Debug functions for testing - can be called from console or connected to buttons
func _input(event):
	if event is InputEventKey and event.pressed:
		# Debug shortcuts for testing
		if event.keycode == KEY_F1:
			# Unlock a random card
			unlock_random_card()
		elif event.keycode == KEY_F2:
			# Unlock all common cards
			SaveSystem.unlock_cards_by_rarity("Common", current_cards)
			refresh_collection()
		elif event.keycode == KEY_F3:
			# Unlock all uncommon cards
			SaveSystem.unlock_cards_by_rarity("Uncommon", current_cards)
			refresh_collection()
		elif event.keycode == KEY_F4:
			# Unlock all rare cards
			SaveSystem.unlock_cards_by_rarity("Rare", current_cards)
			refresh_collection()
		elif event.keycode == KEY_F5:
			# Unlock all legendary cards
			SaveSystem.unlock_cards_by_rarity("Legendary", current_cards)
			refresh_collection()
		elif event.keycode == KEY_F9:
			# Reset collection (for testing)
			SaveSystem.reset_collection()
			refresh_collection()
		elif event.keycode == KEY_F10:
			# Unlock all cards
			SaveSystem.unlock_all_cards(current_cards)
			refresh_collection()
	
	# Existing input handling for preview
	if event is InputEventMouseButton and event.pressed:
		if preview_overlay and preview_overlay.visible:
			# Check if click is outside the preview card (but on the overlay)
			var preview_rect = Rect2(card_preview.global_position, card_preview.size)
			if not preview_rect.has_point(event.global_position):
				hide_card_preview()
	elif event is InputEventKey and event.pressed:
		# Close preview with Escape key
		if event.keycode == KEY_ESCAPE and preview_overlay and preview_overlay.visible:
			hide_card_preview()

func unlock_random_card():
	var locked_cards = []
	for card in current_cards:
		if not SaveSystem.is_card_unlocked(card.name):
			locked_cards.append(card)
	
	if locked_cards.size() > 0:
		var random_card = locked_cards[randi() % locked_cards.size()]
		SaveSystem.unlock_card(random_card.name)
		print("Unlocked: ", random_card.name, " (", random_card.rarity, ")")
		refresh_collection()

func refresh_collection():
	apply_filters()
	update_collection_info()

func setup_filters():
	# Setup faction filter
	faction_filter.add_item("All Factions")
	var factions = CardManager.get_available_factions()
	for faction in factions:
		faction_filter.add_item(faction)
	
	# Setup rarity filter
	rarity_filter.add_item("All Rarities")
	rarity_filter.add_item("Common")
	rarity_filter.add_item("Uncommon")
	rarity_filter.add_item("Rare")
	rarity_filter.add_item("Legendary")

	# Setup set filter
	set_filter.add_item("All Sets")
	var sets = CardManager.get_available_sets()
	for s in sets:
		set_filter.add_item(s)

	# Connect filter signals
	faction_filter.item_selected.connect(_on_faction_filter_changed)
	rarity_filter.item_selected.connect(_on_rarity_filter_changed)
	set_filter.item_selected.connect(_on_set_filter_changed)

func load_card_collection():
	current_cards = CardManager.get_all_cards()
	apply_filters()
	update_collection_info()

func apply_filters():
	# Clear existing cards
	for child in card_grid.get_children():
		child.queue_free()
	
	var filtered_cards = current_cards.duplicate()
	
	# Apply faction filter
	var selected_faction = faction_filter.get_item_text(faction_filter.selected)
	if selected_faction != "All Factions":
		filtered_cards = filtered_cards.filter(func(card): return card.faction == selected_faction)
	
	# Apply rarity filter
	var selected_rarity = rarity_filter.get_item_text(rarity_filter.selected)
	if selected_rarity != "All Rarities":
		filtered_cards = filtered_cards.filter(func(card): return card.rarity == selected_rarity)

	# Apply set filter
	var selected_set = set_filter.get_item_text(set_filter.selected)
	if selected_set != "All Sets":
		filtered_cards = filtered_cards.filter(func(card): return card.card_set == selected_set)
	
	# Sort cards by faction, then by rarity, then by name
	filtered_cards.sort_custom(func(a, b): 
		if a.faction != b.faction:
			return a.faction < b.faction
		if a.rarity != b.rarity:
			var rarity_order = {"Common": 0, "Uncommon": 1, "Rare": 2, "Legendary": 3}
			return rarity_order.get(a.rarity, 0) < rarity_order.get(b.rarity, 0)
		return a.name < b.name
	)
	
	# Create card displays
	for card in filtered_cards:
		create_card_display(card)

func create_card_display(card_data):
	var card_display = card_display_scene.instantiate()
	card_display.setup_card(card_data)
	card_display.card_selected.connect(_on_card_selected)
	
	card_grid.add_child(card_display)

func update_collection_info():
	var total_cards = current_cards.size()
	var unlocked_cards = SaveSystem.get_unlocked_count()
	
	var common_unlocked = SaveSystem.get_unlocked_count_by_rarity("Common", current_cards)
	var common_total = SaveSystem.get_total_count_by_rarity("Common", current_cards)
	
	var uncommon_unlocked = SaveSystem.get_unlocked_count_by_rarity("Uncommon", current_cards)
	var uncommon_total = SaveSystem.get_total_count_by_rarity("Uncommon", current_cards)
	
	var rare_unlocked = SaveSystem.get_unlocked_count_by_rarity("Rare", current_cards)
	var rare_total = SaveSystem.get_total_count_by_rarity("Rare", current_cards)
	
	var legendary_unlocked = SaveSystem.get_unlocked_count_by_rarity("Legendary", current_cards)
	var legendary_total = SaveSystem.get_total_count_by_rarity("Legendary", current_cards)
	
	collection_info.text = "Collection: %d/%d cards | Common: %d/%d | Uncommon: %d/%d | Rare: %d/%d | Legendary: %d/%d" % [
		unlocked_cards, total_cards, 
		common_unlocked, common_total,
		uncommon_unlocked, uncommon_total,
		rare_unlocked, rare_total,
		legendary_unlocked, legendary_total
	]

func _on_faction_filter_changed(_index):
	apply_filters()

func _on_rarity_filter_changed(_index):
	apply_filters()

func _on_set_filter_changed(_index):
	apply_filters()

func _on_card_selected(card_data):
	# Show the card preview when clicked
	show_card_preview(card_data)



func show_card_preview(card_data: Card):
	if not preview_overlay or not card_preview:
		return
	
	current_preview_card = card_data
	
	# Stop any existing tween
	if preview_tween:
		preview_tween.kill()
	
	# Setup the preview card
	setup_preview_card(card_data)
	
	# Show overlay and preview with enhanced animation
	preview_overlay.modulate = Color(1, 1, 1, 0)
	card_preview.modulate = Color(1, 1, 1, 0)
	card_preview.scale = Vector2(0.7, 0.7)
	card_preview.rotation = 0.1  # Slight rotation for dynamic entry
	preview_overlay.visible = true
	
	# Bring overlay to front
	move_child(preview_overlay, get_child_count() - 1)
	
	preview_tween = create_tween()
	preview_tween.set_parallel(true)
	
	# Fade in overlay background
	preview_tween.tween_property(preview_overlay, "modulate", Color(1, 1, 1, 1), 0.2).set_ease(Tween.EASE_OUT)
	
	# Smooth entrance with bounce for card
	preview_tween.tween_property(card_preview, "modulate", Color(1, 1, 1, 1), 0.25).set_ease(Tween.EASE_OUT)
	preview_tween.tween_property(card_preview, "scale", Vector2(1, 1), 0.3).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)
	preview_tween.tween_property(card_preview, "rotation", 0.0, 0.25).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)
	
	# No floating animation - keep preview static

func hide_card_preview():
	if not preview_overlay or not preview_overlay.visible:
		return
	
	current_preview_card = null
	
	# Stop any existing tween
	if preview_tween:
		preview_tween.kill()
	
	preview_tween = create_tween()
	preview_tween.set_parallel(true)
	
	# Fade out overlay and card
	preview_tween.tween_property(preview_overlay, "modulate", Color(1, 1, 1, 0), 0.15)
	preview_tween.tween_property(card_preview, "modulate", Color(1, 1, 1, 0), 0.15)
	preview_tween.tween_property(card_preview, "scale", Vector2(0.9, 0.9), 0.15)
	preview_tween.tween_callback(func(): preview_overlay.visible = false).set_delay(0.15)

func setup_preview_card(card_data: Card):
	var is_unlocked = SaveSystem.is_card_unlocked(card_data.name)
	
	# Update all the preview elements with card data
	var preview_name = card_preview.get_node("PreviewCard/CardInfo/NameContainer/CardName")
	var preview_faction = card_preview.get_node("PreviewCard/CardInfo/FactionContainer/FactionName")
	var preview_rarity = card_preview.get_node("PreviewCard/CardInfo/RarityContainer/RarityName")
	var preview_affinity = card_preview.get_node("PreviewCard/CardInfo/AffinityContainer/AffinityName")
	var preview_set = card_preview.get_node("PreviewCard/CardInfo/SetContainer/SetName")
	
	var preview_top = card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Top")
	var preview_right = card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Right")
	var preview_bottom = card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Bottom")
	var preview_left = card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Left")
	
	var preview_power_sum = card_preview.get_node("PreviewCard/CardInfo/StatsContainer/PowerSum")
	var preview_avg_power = card_preview.get_node("PreviewCard/CardInfo/StatsContainer/AvgPower")
	
	# Set text values based on unlock status
	if is_unlocked:
		if preview_name: preview_name.text = card_data.name
		if preview_faction: preview_faction.text = card_data.faction
		if preview_rarity: preview_rarity.text = card_data.rarity
		if preview_affinity: preview_affinity.text = card_data.affinity
		if preview_set: preview_set.text = card_data.card_set
		
		if preview_top: preview_top.text = str(card_data.top)
		if preview_right: preview_right.text = str(card_data.right)
		if preview_bottom: preview_bottom.text = str(card_data.bottom)
		if preview_left: preview_left.text = str(card_data.left)
		
		if preview_power_sum: preview_power_sum.text = "Total: " + str(card_data.power_sum)
		if preview_avg_power: preview_avg_power.text = "Avg: " + str(card_data.average_power)
	else:
		# Show placeholder text for locked cards
		if preview_name: preview_name.text = "???"
		if preview_faction: preview_faction.text = "???"
		if preview_rarity: preview_rarity.text = card_data.rarity  # Show rarity even when locked
		if preview_affinity: preview_affinity.text = "???"
		if preview_set: preview_set.text = "???"
		
		if preview_top: preview_top.text = "?"
		if preview_right: preview_right.text = "?"
		if preview_bottom: preview_bottom.text = "?"
		if preview_left: preview_left.text = "?"
		
		if preview_power_sum: preview_power_sum.text = "Total: ?"
		if preview_avg_power: preview_avg_power.text = "Avg: ?"
	
	# Apply rarity styling
	apply_preview_rarity_styling(card_data, is_unlocked)
	
	# Load card image
	load_preview_card_image(card_data, is_unlocked)

func apply_preview_rarity_styling(card_data: Card, is_unlocked: bool = true):
	var preview_card = card_preview.get_node("PreviewCard")
	var card_art = card_preview.get_node("PreviewCard/CardArt")
	
	if not preview_card or not card_art:
		return
	
	# Create main card background with gradient
	var main_style = StyleBoxFlat.new()
	main_style.corner_radius_top_left = 12
	main_style.corner_radius_top_right = 12
	main_style.corner_radius_bottom_left = 12
	main_style.corner_radius_bottom_right = 12
	
	# Create card art background
	var art_style = StyleBoxFlat.new()
	art_style.corner_radius_top_left = 8
	art_style.corner_radius_top_right = 8
	art_style.corner_radius_bottom_left = 8
	art_style.corner_radius_bottom_right = 8
	
	match card_data.rarity:
		"Common":
			# Silver theme
			main_style.bg_color = Color(0.15, 0.15, 0.2, 0.98)
			main_style.border_color = Color(0.8, 0.8, 0.9, 1.0)
			main_style.border_width_left = 2
			main_style.border_width_right = 2
			main_style.border_width_top = 2
			main_style.border_width_bottom = 2
			
			art_style.bg_color = Color(0.2, 0.2, 0.25, 0.9)
			art_style.border_color = Color(0.7, 0.7, 0.8, 1.0)
			art_style.border_width_left = 2
			art_style.border_width_right = 2
			art_style.border_width_top = 2
			art_style.border_width_bottom = 2
			
		"Uncommon":
			# Green theme with glow
			main_style.bg_color = Color(0.05, 0.2, 0.05, 0.98)
			main_style.border_color = Color(0.2, 0.9, 0.3, 1.0)
			main_style.border_width_left = 3
			main_style.border_width_right = 3
			main_style.border_width_top = 3
			main_style.border_width_bottom = 3
			main_style.shadow_color = Color(0.1, 0.6, 0.2, 0.6)
			main_style.shadow_size = 8
			
			art_style.bg_color = Color(0.1, 0.25, 0.1, 0.9)
			art_style.border_color = Color(0.3, 0.8, 0.4, 1.0)
			art_style.border_width_left = 2
			art_style.border_width_right = 2
			art_style.border_width_top = 2
			art_style.border_width_bottom = 2
			
		"Rare":
			# Golden theme with strong glow
			main_style.bg_color = Color(0.25, 0.15, 0.0, 0.98)
			main_style.border_color = Color(1.0, 0.8, 0.0, 1.0)
			main_style.border_width_left = 4
			main_style.border_width_right = 4
			main_style.border_width_top = 4
			main_style.border_width_bottom = 4
			main_style.shadow_color = Color(0.8, 0.6, 0.0, 0.8)
			main_style.shadow_size = 12
			
			art_style.bg_color = Color(0.3, 0.2, 0.05, 0.9)
			art_style.border_color = Color(0.9, 0.7, 0.1, 1.0)
			art_style.border_width_left = 3
			art_style.border_width_right = 3
			art_style.border_width_top = 3
			art_style.border_width_bottom = 3
			
		"Legendary":
			# Purple/magenta theme with epic glow
			main_style.bg_color = Color(0.15, 0.05, 0.25, 0.98)
			main_style.border_color = Color(0.9, 0.2, 0.9, 1.0)
			main_style.border_width_left = 5
			main_style.border_width_right = 5
			main_style.border_width_top = 5
			main_style.border_width_bottom = 5
			main_style.shadow_color = Color(0.7, 0.2, 0.8, 0.9)
			main_style.shadow_size = 16
			
			art_style.bg_color = Color(0.2, 0.1, 0.3, 0.9)
			art_style.border_color = Color(0.8, 0.3, 0.9, 1.0)
			art_style.border_width_left = 3
			art_style.border_width_right = 3
			art_style.border_width_top = 3
			art_style.border_width_bottom = 3
	
	# Apply styles
	preview_card.add_theme_stylebox_override("panel", main_style)
	card_art.add_theme_stylebox_override("panel", art_style)
	
	# Style the power numbers with enhanced effects
	style_preview_power_numbers(card_data)
	
	# Add animated effects for higher rarities
	add_preview_rarity_effects(card_data)

func style_preview_power_numbers(card_data: Card):
	var power_labels = [
		card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Top"),
		card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Right"),
		card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Bottom"),
		card_preview.get_node("PreviewCard/CardArt/PowerNumbers/Left")
	]
	
	for label in power_labels:
		if not label:
			continue
		
		var label_settings = LabelSettings.new()
		label_settings.font_size = 24
		label_settings.font_color = Color.WHITE
		
		match card_data.rarity:
			"Common":
				label_settings.outline_size = 2
				label_settings.outline_color = Color.BLACK
				label_settings.shadow_size = 1
				label_settings.shadow_color = Color(0, 0, 0, 0.8)
				label_settings.shadow_offset = Vector2(1, 1)
			"Uncommon":
				label_settings.outline_size = 3
				label_settings.outline_color = Color(0.1, 0.4, 0.1, 1.0)
				label_settings.shadow_size = 2
				label_settings.shadow_color = Color(0.2, 0.8, 0.3, 0.8)
				label_settings.shadow_offset = Vector2(1, 1)
			"Rare":
				label_settings.font_color = Color(1.0, 0.9, 0.6, 1.0)
				label_settings.outline_size = 4
				label_settings.outline_color = Color(0.4, 0.2, 0.0, 1.0)
				label_settings.shadow_size = 3
				label_settings.shadow_color = Color(1.0, 0.8, 0.0, 0.9)
				label_settings.shadow_offset = Vector2(2, 2)
			"Legendary":
				label_settings.font_color = Color(1.0, 0.8, 1.0, 1.0)
				label_settings.outline_size = 5
				label_settings.outline_color = Color(0.3, 0.1, 0.4, 1.0)
				label_settings.shadow_size = 4
				label_settings.shadow_color = Color(0.9, 0.2, 0.9, 1.0)
				label_settings.shadow_offset = Vector2(2, 2)
		
		label.label_settings = label_settings

func add_preview_rarity_effects(card_data: Card):
	# Remove existing effects
	var existing_effects = card_preview.find_child("RarityEffects")
	if existing_effects:
		existing_effects.queue_free()
	
	# Only add effects for rare and legendary cards
	if card_data.rarity != "Rare" and card_data.rarity != "Legendary":
		return
	
	var effects_container = Control.new()
	effects_container.name = "RarityEffects"
	effects_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	effects_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	card_preview.add_child(effects_container)
	
	if card_data.rarity == "Rare":
		add_golden_particle_effect(effects_container)
	elif card_data.rarity == "Legendary":
		add_legendary_aura_effect(effects_container)

func add_golden_particle_effect(container: Control):
	# Create subtle golden sparkle effect for rare cards
	for i in range(3):
		var sparkle = ColorRect.new()
		sparkle.size = Vector2(4, 4)
		sparkle.color = Color(1.0, 0.8, 0.2, 0.8)
		sparkle.position = Vector2(
			randf() * container.size.x,
			randf() * container.size.y
		)
		container.add_child(sparkle)
		
		# Animate sparkle
		var tween = create_tween()
		tween.set_loops()
		tween.tween_property(sparkle, "modulate", Color(1.0, 1.0, 0.4, 1.0), 1.0)
		tween.tween_property(sparkle, "modulate", Color(1.0, 0.8, 0.2, 0.3), 1.0)

func add_legendary_aura_effect(container: Control):
	# Create pulsing aura effect for legendary cards
	var aura = ColorRect.new()
	aura.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	aura.color = Color(0.8, 0.2, 0.8, 0.1)
	aura.mouse_filter = Control.MOUSE_FILTER_IGNORE
	container.add_child(aura)
	
	# Animate aura
	var tween = create_tween()
	tween.set_loops()
	tween.tween_property(aura, "modulate", Color(1.2, 0.4, 1.2, 0.3), 1.5)
	tween.tween_property(aura, "modulate", Color(0.8, 0.2, 0.8, 0.1), 1.5)

func clear_all_preview_images(card_art: Control):
	# Remove ALL children that could be images or placeholders
	var children_to_remove = []
	for child in card_art.get_children():
		if child.name == "CardImage" or child is TextureRect or child is ColorRect:
			children_to_remove.append(child)
	
	# Remove them immediately
	for child in children_to_remove:
		card_art.remove_child(child)
		child.queue_free()

func load_preview_card_image(card_data: Card, is_unlocked: bool = true):
	var card_art = card_preview.get_node("PreviewCard/CardArt")
	if not card_art:
		return
	
	# Remove ALL existing images immediately to prevent bleed-through
	clear_all_preview_images(card_art)
	
	if not is_unlocked:
		# Create locked placeholder for preview with rounded corners
		var placeholder = Panel.new()
		placeholder.name = "CardImage"
		placeholder.mouse_filter = Control.MOUSE_FILTER_IGNORE
		placeholder.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		
		# Create rounded style for placeholder
		var placeholder_style = StyleBoxFlat.new()
		placeholder_style.bg_color = Color(0.1, 0.1, 0.1, 0.95)
		placeholder_style.corner_radius_top_left = 8
		placeholder_style.corner_radius_top_right = 8
		placeholder_style.corner_radius_bottom_left = 8
		placeholder_style.corner_radius_bottom_right = 8
		placeholder.add_theme_stylebox_override("panel", placeholder_style)
		
		# Add a large lock symbol
		var lock_label = Label.new()
		lock_label.text = "🔒"
		lock_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		lock_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
		lock_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		lock_label.mouse_filter = Control.MOUSE_FILTER_IGNORE
		
		# Style the lock label for preview
		var label_settings = LabelSettings.new()
		label_settings.font_size = 64
		label_settings.font_color = Color(0.3, 0.3, 0.3, 0.8)
		lock_label.label_settings = label_settings
		
		placeholder.add_child(lock_label)
		card_art.add_child(placeholder)
		card_art.move_child(placeholder, 0)
		return
	
	if card_data.image_path.is_empty():
		return
	
	var full_path = "res://" + card_data.image_path
	var texture: Texture2D = null
	
	# Try loading the image using the same method as CardDisplay
	if FileAccess.file_exists(full_path):
		var image = Image.new()
		var file_bytes = FileAccess.get_file_as_bytes(full_path)
		if file_bytes.size() > 0:
			# Since the files are JPEG with .png extension, try JPEG loading first
			var error = image.load_jpg_from_buffer(file_bytes)
			if error == OK:
				texture = ImageTexture.new()
				texture.set_image(image)
			else:
				# Try PNG if JPEG fails
				error = image.load_png_from_buffer(file_bytes)
				if error == OK:
					texture = ImageTexture.new()
					texture.set_image(image)
	
	# Method 2: Try ResourceLoader if ImageTexture failed
	if not texture and ResourceLoader.exists(full_path):
		texture = ResourceLoader.load(full_path) as Texture2D
	
	# Fallback to placeholder if original fails
	if not texture:
		var placeholder_path = "res://data/card_images/placeholder.png"
		if FileAccess.file_exists(placeholder_path):
			var image = Image.new()
			if image.load(placeholder_path) == OK:
				texture = ImageTexture.new()
				texture.set_image(image)
		
		# If ImageTexture placeholder fails, try ResourceLoader
		if not texture and ResourceLoader.exists(placeholder_path):
			texture = ResourceLoader.load(placeholder_path) as Texture2D
	
	if texture:
		# Create a container panel with rounded corners for clipping
		var image_container = Panel.new()
		image_container.name = "CardImage"
		image_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
		image_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		image_container.clip_contents = true
		
		# Create rounded style for the container
		var container_style = StyleBoxFlat.new()
		container_style.bg_color = Color.TRANSPARENT
		container_style.corner_radius_top_left = 8
		container_style.corner_radius_top_right = 8
		container_style.corner_radius_bottom_left = 8
		container_style.corner_radius_bottom_right = 8
		image_container.add_theme_stylebox_override("panel", container_style)
		
		# Create the actual image rect inside the container
		var image_rect = TextureRect.new()
		image_rect.texture = texture
		image_rect.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
		image_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
		image_rect.mouse_filter = Control.MOUSE_FILTER_IGNORE
		image_rect.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		
		# Add image to container, then container to card art
		image_container.add_child(image_rect)
		card_art.add_child(image_container)
		card_art.move_child(image_container, 0)

func setup_debug_panel():
	# Create a simple debug info label that shows available shortcuts
	var debug_info = Label.new()
	debug_info.name = "DebugInfo"
	debug_info.text = "Debug: F1=Random Card | F2=Commons | F3=Uncommons | F4=Rares | F5=Legendaries | F9=Reset | F10=All"
	debug_info.position = Vector2(10, 10)
	debug_info.z_index = 100
	debug_info.modulate = Color(0.8, 0.8, 0.8, 0.7)
	
	# Style the debug label
	var label_settings = LabelSettings.new()
	label_settings.font_size = 12
	label_settings.font_color = Color.WHITE
	label_settings.outline_size = 1
	label_settings.outline_color = Color.BLACK
	debug_info.label_settings = label_settings
	
	add_child(debug_info)
