class_name FantasyTheme
extends RefCounted

# Fantasy Color Palette
static var COLORS = {
	# Primary Fantasy Colors
	"mystic_gold": Color(0.95, 0.8, 0.2, 1.0),
	"arcane_purple": Color(0.6, 0.2, 0.8, 1.0),
	"ethereal_blue": Color(0.2, 0.6, 0.9, 1.0),
	"enchanted_green": Color(0.2, 0.8, 0.4, 1.0),
	"dragon_red": Color(0.9, 0.2, 0.2, 1.0),
	"shadow_black": Color(0.1, 0.1, 0.15, 1.0),
	"moonlight_silver": Color(0.8, 0.85, 0.9, 1.0),
	
	# Background Colors
	"deep_void": Color(0.05, 0.05, 0.1, 1.0),
	"dark_stone": Color(0.15, 0.12, 0.18, 1.0),
	"ancient_parchment": Color(0.9, 0.85, 0.7, 1.0),
	"mystical_mist": Color(0.3, 0.25, 0.35, 0.8),
	
	# Rarity Colors Enhanced
	"common_steel": Color(0.7, 0.75, 0.8, 1.0),
	"uncommon_emerald": Color(0.2, 0.8, 0.3, 1.0),
	"rare_gold": Color(1.0, 0.8, 0.0, 1.0),
	"legendary_amethyst": Color(0.8, 0.2, 0.9, 1.0),
	"mythic_rainbow": Color(1.0, 0.5, 0.8, 1.0),
	
	# Glow Effects
	"soft_glow": Color(1.0, 1.0, 1.0, 0.3),
	"magic_aura": Color(0.8, 0.6, 1.0, 0.4),
	"fire_glow": Color(1.0, 0.6, 0.2, 0.5),
	"ice_glow": Color(0.6, 0.8, 1.0, 0.4),
	"nature_glow": Color(0.4, 0.9, 0.3, 0.4)
}

# Create fantasy-styled panels with magical borders
static func create_fantasy_panel(rarity: String = "Common", has_glow: bool = false) -> StyleBoxFlat:
	var style = StyleBoxFlat.new()
	
	# Base styling
	style.corner_radius_top_left = 12
	style.corner_radius_top_right = 12
	style.corner_radius_bottom_left = 12
	style.corner_radius_bottom_right = 12
	
	match rarity:
		"Common":
			style.bg_color = COLORS.dark_stone
			style.border_color = COLORS.common_steel
			style.border_width_left = 2
			style.border_width_right = 2
			style.border_width_top = 2
			style.border_width_bottom = 2
			if has_glow:
				style.shadow_color = Color(COLORS.common_steel.r, COLORS.common_steel.g, COLORS.common_steel.b, 0.4)
				style.shadow_size = 4
		
		"Uncommon":
			style.bg_color = Color(0.05, 0.2, 0.05, 0.95)
			style.border_color = COLORS.uncommon_emerald
			style.border_width_left = 3
			style.border_width_right = 3
			style.border_width_top = 3
			style.border_width_bottom = 3
			if has_glow:
				style.shadow_color = Color(COLORS.uncommon_emerald.r, COLORS.uncommon_emerald.g, COLORS.uncommon_emerald.b, 0.6)
				style.shadow_size = 8
		
		"Rare":
			style.bg_color = Color(0.25, 0.15, 0.0, 0.95)
			style.border_color = COLORS.rare_gold
			style.border_width_left = 4
			style.border_width_right = 4
			style.border_width_top = 4
			style.border_width_bottom = 4
			if has_glow:
				style.shadow_color = Color(COLORS.rare_gold.r, COLORS.rare_gold.g, COLORS.rare_gold.b, 0.8)
				style.shadow_size = 12
		
		"Legendary":
			style.bg_color = Color(0.15, 0.05, 0.25, 0.95)
			style.border_color = COLORS.legendary_amethyst
			style.border_width_left = 5
			style.border_width_right = 5
			style.border_width_top = 5
			style.border_width_bottom = 5
			if has_glow:
				style.shadow_color = Color(COLORS.legendary_amethyst.r, COLORS.legendary_amethyst.g, COLORS.legendary_amethyst.b, 0.9)
				style.shadow_size = 16
	
	return style

# Create fantasy button styles
static func create_fantasy_button(button_type: String = "primary", is_hover: bool = false) -> StyleBoxFlat:
	var style = StyleBoxFlat.new()
	
	# Consistent border radius
	style.corner_radius_top_left = 8
	style.corner_radius_top_right = 8
	style.corner_radius_bottom_left = 8
	style.corner_radius_bottom_right = 8
	
	# Add padding for better text layout
	style.content_margin_left = 12
	style.content_margin_right = 12
	style.content_margin_top = 8
	style.content_margin_bottom = 8
	
	match button_type:
		"primary":
			style.bg_color = Color(0.2, 0.4, 0.7, 0.9)
			style.border_color = COLORS.ethereal_blue
			style.border_width_left = 2
			style.border_width_right = 2
			style.border_width_top = 2
			style.border_width_bottom = 2
			if is_hover:
				style.bg_color = Color(0.25, 0.5, 0.8, 0.95)
				style.shadow_color = Color(COLORS.ethereal_blue.r, COLORS.ethereal_blue.g, COLORS.ethereal_blue.b, 0.8)
				style.shadow_size = 10
			else:
				style.shadow_color = Color(COLORS.ethereal_blue.r, COLORS.ethereal_blue.g, COLORS.ethereal_blue.b, 0.5)
				style.shadow_size = 6
		
		"secondary":
			style.bg_color = Color(0.3, 0.25, 0.35, 0.9)
			style.border_color = COLORS.moonlight_silver
			style.border_width_left = 1
			style.border_width_right = 1
			style.border_width_top = 1
			style.border_width_bottom = 1
			if is_hover:
				style.bg_color = Color(0.4, 0.35, 0.45, 0.95)
				style.shadow_color = Color(COLORS.moonlight_silver.r, COLORS.moonlight_silver.g, COLORS.moonlight_silver.b, 0.6)
				style.shadow_size = 8
			else:
				style.shadow_color = Color(COLORS.moonlight_silver.r, COLORS.moonlight_silver.g, COLORS.moonlight_silver.b, 0.3)
				style.shadow_size = 4
		
		"danger":
			style.bg_color = Color(0.4, 0.1, 0.1, 0.9)
			style.border_color = COLORS.dragon_red
			style.border_width_left = 2
			style.border_width_right = 2
			style.border_width_top = 2
			style.border_width_bottom = 2
			if is_hover:
				style.bg_color = Color(0.5, 0.15, 0.15, 0.95)
				style.shadow_color = Color(COLORS.dragon_red.r, COLORS.dragon_red.g, COLORS.dragon_red.b, 0.8)
				style.shadow_size = 10
			else:
				style.shadow_color = Color(COLORS.dragon_red.r, COLORS.dragon_red.g, COLORS.dragon_red.b, 0.5)
				style.shadow_size = 6
		
		"success":
			style.bg_color = Color(0.1, 0.3, 0.1, 0.9)
			style.border_color = COLORS.enchanted_green
			style.border_width_left = 2
			style.border_width_right = 2
			style.border_width_top = 2
			style.border_width_bottom = 2
			if is_hover:
				style.bg_color = Color(0.15, 0.4, 0.15, 0.95)
				style.shadow_color = Color(COLORS.enchanted_green.r, COLORS.enchanted_green.g, COLORS.enchanted_green.b, 0.8)
				style.shadow_size = 10
			else:
				style.shadow_color = Color(COLORS.enchanted_green.r, COLORS.enchanted_green.g, COLORS.enchanted_green.b, 0.5)
				style.shadow_size = 6
	
	return style

# Create enhanced label settings with fantasy styling
static func create_fantasy_label(text_type: String = "normal", rarity: String = "Common") -> LabelSettings:
	var settings = LabelSettings.new()
	
	match text_type:
		"title":
			settings.font_size = 28  # Reduced from 32 for better fit
			settings.font_color = COLORS.mystic_gold
			settings.outline_size = 2  # Reduced from 3 for cleaner look
			settings.outline_color = COLORS.shadow_black
			settings.shadow_size = 2
			settings.shadow_color = Color(0, 0, 0, 0.8)
			settings.shadow_offset = Vector2(2, 2)
		
		"subtitle":
			settings.font_size = 16  # Reduced from 24 for better fit
			settings.font_color = COLORS.moonlight_silver
			settings.outline_size = 1  # Reduced from 2 for cleaner look
			settings.outline_color = COLORS.shadow_black
			settings.shadow_size = 1
			settings.shadow_color = Color(0, 0, 0, 0.6)
			settings.shadow_offset = Vector2(1, 1)
		
		"power_number":
			settings.font_size = 20
			settings.font_color = Color.WHITE
			
			match rarity:
				"Common":
					settings.outline_size = 2
					settings.outline_color = COLORS.shadow_black
					settings.shadow_size = 1
					settings.shadow_color = Color(0, 0, 0, 0.8)
				"Uncommon":
					settings.outline_size = 3
					settings.outline_color = Color(0.1, 0.4, 0.1, 1.0)
					settings.shadow_size = 2
					settings.shadow_color = COLORS.nature_glow
				"Rare":
					settings.font_color = Color(1.0, 0.9, 0.6, 1.0)
					settings.outline_size = 4
					settings.outline_color = Color(0.4, 0.2, 0.0, 1.0)
					settings.shadow_size = 3
					settings.shadow_color = COLORS.fire_glow
				"Legendary":
					settings.font_color = Color(1.0, 0.8, 1.0, 1.0)
					settings.outline_size = 5
					settings.outline_color = Color(0.3, 0.1, 0.4, 1.0)
					settings.shadow_size = 4
					settings.shadow_color = COLORS.magic_aura
			
			settings.shadow_offset = Vector2(1, 1)
		
		"button":
			settings.font_size = 14  # Good size for button text
			settings.font_color = COLORS.moonlight_silver
			settings.outline_size = 1
			settings.outline_color = COLORS.shadow_black
			settings.shadow_size = 1
			settings.shadow_color = Color(0, 0, 0, 0.5)
			settings.shadow_offset = Vector2(1, 1)
		
		"normal":
			settings.font_size = 14  # Reduced from 16 for better fit
			settings.font_color = COLORS.moonlight_silver
			settings.outline_size = 1
			settings.outline_color = COLORS.shadow_black
	
	return settings

# Create magical particle effects using simple animated elements
static func create_particle_system(parent: Node, effect_type: String = "sparkles"):
	var particle_container = Control.new()
	particle_container.name = "MagicalParticles"
	particle_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	particle_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	match effect_type:
		"sparkles":
			create_sparkle_effects(particle_container)
		"mystical_aura":
			create_aura_effects(particle_container)
		"fire_embers":
			create_ember_effects(particle_container)
	
	parent.add_child(particle_container)
	return particle_container

# Create sparkle effects using animated ColorRects
static func create_sparkle_effects(container: Control):
	for i in range(8):
		var sparkle = ColorRect.new()
		sparkle.size = Vector2(4, 4)
		sparkle.color = Color(COLORS.mystic_gold.r, COLORS.mystic_gold.g, COLORS.mystic_gold.b, 0.8)
		sparkle.position = Vector2(
			randf() * container.size.x if container.size.x > 0 else randf() * 400,
			randf() * container.size.y if container.size.y > 0 else randf() * 300
		)
		
		# Make sparkles diamond-shaped
		sparkle.rotation = PI / 4
		
		container.add_child(sparkle)
		
		# Animate sparkle movement and fading
		var tween = container.create_tween()
		tween.set_loops()
		tween.set_parallel(true)
		
		# Float upward
		tween.tween_property(sparkle, "position:y", sparkle.position.y - 100, 2.0 + randf())
		tween.tween_callback(func(): sparkle.position.y += 100).set_delay(2.0 + randf())
		
		# Fade in and out
		tween.tween_property(sparkle, "modulate:a", 1.0, 0.5)
		tween.tween_property(sparkle, "modulate:a", 0.2, 1.5).set_delay(0.5)

# Create mystical aura effects
static func create_aura_effects(container: Control):
	for i in range(5):
		var aura = ColorRect.new()
		aura.size = Vector2(30 + i * 10, 30 + i * 10)
		aura.color = Color(COLORS.magic_aura.r, COLORS.magic_aura.g, COLORS.magic_aura.b, 0.1 + i * 0.05)
		aura.position = Vector2(
			randf() * (container.size.x - aura.size.x) if container.size.x > 0 else randf() * 300,
			randf() * (container.size.y - aura.size.y) if container.size.y > 0 else randf() * 200
		)
		
		# Make auras circular
		var style = StyleBoxFlat.new()
		style.bg_color = aura.color
		var radius = aura.size.x / 2
		style.corner_radius_top_left = radius
		style.corner_radius_top_right = radius
		style.corner_radius_bottom_left = radius
		style.corner_radius_bottom_right = radius
		aura.add_theme_stylebox_override("panel", style)
		
		container.add_child(aura)
		
		# Animate aura pulsing and rotation
		var pulse_tween = container.create_tween()
		pulse_tween.set_loops()
		pulse_tween.tween_property(aura, "scale", Vector2(1.2, 1.2), 1.5 + i * 0.3)
		pulse_tween.tween_property(aura, "scale", Vector2(0.8, 0.8), 1.5 + i * 0.3)
		
		var rotation_tween = container.create_tween()
		rotation_tween.set_loops()
		rotation_tween.tween_property(aura, "rotation", TAU, 8.0 + i * 2.0)

# Create fire ember effects
static func create_ember_effects(container: Control):
	for i in range(12):
		var ember = ColorRect.new()
		ember.size = Vector2(3, 3)
		ember.color = Color(COLORS.fire_glow.r, COLORS.fire_glow.g, COLORS.fire_glow.b, 0.7)
		ember.position = Vector2(
			randf() * container.size.x if container.size.x > 0 else randf() * 400,
			container.size.y if container.size.y > 0 else 300
		)
		
		container.add_child(ember)
		
		# Animate ember floating upward with slight sway
		var tween = container.create_tween()
		tween.set_loops()
		tween.set_parallel(true)
		
		# Float upward
		tween.tween_property(ember, "position:y", ember.position.y - 150, 1.5 + randf() * 0.5)
		tween.tween_callback(func(): ember.position.y += 150).set_delay(1.5 + randf() * 0.5)
		
		# Sway left and right
		tween.tween_property(ember, "position:x", ember.position.x + 20, 0.8)
		tween.tween_property(ember, "position:x", ember.position.x - 20, 0.8).set_delay(0.8)
		
		# Fade out as it rises
		tween.tween_property(ember, "modulate:a", 0.0, 1.5)
		tween.tween_property(ember, "modulate:a", 0.7, 0.1).set_delay(1.5)

# Helper functions to create simple textures for particles
static func create_sparkle_texture() -> ImageTexture:
	var image = Image.create(8, 8, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	# Create a simple star/sparkle pattern
	for x in range(8):
		for y in range(8):
			var center_x = 4
			var center_y = 4
			var dist = sqrt((x - center_x) * (x - center_x) + (y - center_y) * (y - center_y))
			
			if dist < 2.0:
				var alpha = 1.0 - (dist / 2.0)
				image.set_pixel(x, y, Color(1, 1, 1, alpha))
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	return texture

static func create_aura_texture() -> ImageTexture:
	var image = Image.create(16, 16, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	# Create a soft circular gradient
	for x in range(16):
		for y in range(16):
			var center_x = 8
			var center_y = 8
			var dist = sqrt((x - center_x) * (x - center_x) + (y - center_y) * (y - center_y))
			
			if dist < 8.0:
				var alpha = 0.8 - (dist / 8.0)
				image.set_pixel(x, y, Color(1, 1, 1, alpha))
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	return texture

static func create_ember_texture() -> ImageTexture:
	var image = Image.create(6, 6, false, Image.FORMAT_RGBA8)
	image.fill(Color.TRANSPARENT)
	
	# Create a small glowing dot
	for x in range(6):
		for y in range(6):
			var center_x = 3
			var center_y = 3
			var dist = sqrt((x - center_x) * (x - center_x) + (y - center_y) * (y - center_y))
			
			if dist < 3.0:
				var alpha = 1.0 - (dist / 3.0)
				image.set_pixel(x, y, Color(1, 0.6, 0.2, alpha))
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	return texture

# Apply fantasy theme to any control node
static func apply_fantasy_theme(node: Control, theme_type: String = "default"):
	match theme_type:
		"main_background":
			if node is ColorRect:
				node.color = COLORS.deep_void
		
		"card_collection":
			if node is ColorRect:
				# Create gradient background
				node.color = COLORS.deep_void
		
		"button_primary":
			if node is Button:
				node.add_theme_stylebox_override("normal", create_fantasy_button("primary"))
				node.add_theme_stylebox_override("hover", create_fantasy_button("primary"))
				node.add_theme_stylebox_override("pressed", create_fantasy_button("primary"))
		
		"panel_mystical":
			if node is Panel:
				node.add_theme_stylebox_override("panel", create_fantasy_panel("Rare", true))

# Create animated background effects
static func create_animated_background(parent: Control):
	# Create floating mystical orbs
	for i in range(5):
		var orb = ColorRect.new()
		orb.size = Vector2(20, 20)
		orb.color = Color(COLORS.magic_aura.r, COLORS.magic_aura.g, COLORS.magic_aura.b, 0.3)
		orb.position = Vector2(
			randf() * parent.size.x,
			randf() * parent.size.y
		)
		
		# Make orbs circular
		var style = StyleBoxFlat.new()
		style.bg_color = orb.color
		style.corner_radius_top_left = 10
		style.corner_radius_top_right = 10
		style.corner_radius_bottom_left = 10
		style.corner_radius_bottom_right = 10
		orb.add_theme_stylebox_override("panel", style)
		
		parent.add_child(orb)
		
		# Animate floating motion
		var tween = parent.create_tween()
		tween.set_loops()
		tween.tween_property(orb, "position:y", orb.position.y - 100, 3.0 + randf() * 2.0)
		tween.tween_property(orb, "position:y", orb.position.y + 100, 3.0 + randf() * 2.0)
		
		# Animate opacity
		var fade_tween = parent.create_tween()
		fade_tween.set_loops()
		fade_tween.tween_property(orb, "modulate:a", 0.1, 2.0 + randf())
		fade_tween.tween_property(orb, "modulate:a", 0.6, 2.0 + randf())
