extends Control

@onready var background = $Background
@onready var demo_container = $DemoContainer
@onready var title_label = $DemoContainer/VBoxContainer/Title

@onready var common_button = $DemoContainer/VBoxContainer/ButtonContainer/CommonButton
@onready var uncommon_button = $DemoContainer/VBoxContainer/ButtonContainer/UncommonButton
@onready var rare_button = $DemoContainer/VBoxContainer/ButtonContainer/RareButton
@onready var legendary_button = $DemoContainer/VBoxContainer/ButtonContainer/LegendaryButton
@onready var back_button = $DemoContainer/VBoxContainer/BackButton

@onready var power_labels = [
	$DemoContainer/VBoxContainer/PowerNumberDemo/PowerLabel1,
	$DemoContainer/VBoxContainer/PowerNumberDemo/PowerLabel2,
	$DemoContainer/VBoxContainer/PowerNumberDemo/PowerLabel3,
	$DemoContainer/VBoxContainer/PowerNumberDemo/PowerLabel4
]

func _ready():
	apply_fantasy_demo_styling()
	setup_demo_effects()
	connect_demo_signals()

func apply_fantasy_demo_styling():
	# Apply fantasy background
	if background:
		background.color = FantasyTheme.COLORS.deep_void
	
	# Style the main demo container
	if demo_container:
		var container_style = FantasyTheme.create_fantasy_panel("Legendary", true)
		demo_container.add_theme_stylebox_override("panel", container_style)
	
	# Style the title
	if title_label:
		title_label.label_settings = FantasyTheme.create_fantasy_label("title")
	
	# Style buttons with different rarities
	if common_button:
		var style = FantasyTheme.create_fantasy_panel("Common", true)
		common_button.add_theme_stylebox_override("normal", style)
		common_button.add_theme_color_override("font_color", FantasyTheme.COLORS.common_steel)
	
	if uncommon_button:
		var style = FantasyTheme.create_fantasy_panel("Uncommon", true)
		uncommon_button.add_theme_stylebox_override("normal", style)
		uncommon_button.add_theme_color_override("font_color", FantasyTheme.COLORS.uncommon_emerald)
	
	if rare_button:
		var style = FantasyTheme.create_fantasy_panel("Rare", true)
		rare_button.add_theme_stylebox_override("normal", style)
		rare_button.add_theme_color_override("font_color", FantasyTheme.COLORS.rare_gold)
	
	if legendary_button:
		var style = FantasyTheme.create_fantasy_panel("Legendary", true)
		legendary_button.add_theme_stylebox_override("normal", style)
		legendary_button.add_theme_color_override("font_color", FantasyTheme.COLORS.legendary_amethyst)
	
	if back_button:
		back_button.add_theme_stylebox_override("normal", FantasyTheme.create_fantasy_button("secondary"))
		back_button.add_theme_color_override("font_color", FantasyTheme.COLORS.moonlight_silver)
	
	# Style power number labels with different rarities
	var rarities = ["Common", "Uncommon", "Rare", "Legendary"]
	for i in range(power_labels.size()):
		if power_labels[i] and i < rarities.size():
			power_labels[i].label_settings = FantasyTheme.create_fantasy_label("power_number", rarities[i])

func setup_demo_effects():
	# Create animated background effects
	FantasyTheme.create_animated_background(self)
	
	# Add floating mystical particles
	var particle_container = Control.new()
	particle_container.name = "DemoMagicalParticles"
	particle_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	particle_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	particle_container.z_index = -2
	add_child(particle_container)
	
	# Create sparkle effects
	FantasyTheme.create_particle_system(particle_container, "sparkles")
	FantasyTheme.create_particle_system(particle_container, "mystical_aura")

func connect_demo_signals():
	if back_button:
		back_button.pressed.connect(_on_back_button_pressed)
	
	# Connect rarity buttons to show different effects
	if common_button:
		common_button.pressed.connect(_on_rarity_button_pressed.bind("Common"))
	if uncommon_button:
		uncommon_button.pressed.connect(_on_rarity_button_pressed.bind("Uncommon"))
	if rare_button:
		rare_button.pressed.connect(_on_rarity_button_pressed.bind("Rare"))
	if legendary_button:
		legendary_button.pressed.connect(_on_rarity_button_pressed.bind("Legendary"))

func _on_back_button_pressed():
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _on_rarity_button_pressed(rarity: String):
	print("Demonstrating ", rarity, " rarity effects!")
	
	# Create temporary effect showcase
	var effect_container = Control.new()
	effect_container.name = "TempEffect"
	effect_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	effect_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	add_child(effect_container)
	
	match rarity:
		"Common":
			# Simple sparkle
			FantasyTheme.create_particle_system(effect_container, "sparkles")
		"Uncommon":
			# Green mystical aura
			FantasyTheme.create_particle_system(effect_container, "mystical_aura")
		"Rare":
			# Golden fire embers
			FantasyTheme.create_particle_system(effect_container, "fire_embers")
		"Legendary":
			# All effects combined
			FantasyTheme.create_particle_system(effect_container, "sparkles")
			FantasyTheme.create_particle_system(effect_container, "mystical_aura")
			FantasyTheme.create_particle_system(effect_container, "fire_embers")
	
	# Remove effect after 3 seconds
	var timer = Timer.new()
	timer.wait_time = 3.0
	timer.one_shot = true
	timer.timeout.connect(func(): 
		if effect_container:
			effect_container.queue_free()
		timer.queue_free()
	)
	add_child(timer)
	timer.start()