extends Control

@onready var board = $HBoxContainer/GameArea/BoardContainer/Board
@onready var player_hand = $HBoxContainer/LeftPanel/PlayerHand
@onready var enemy_hand = $HBoxContainer/RightPanel/EnemyHand
@onready var score_label = $HBoxContainer/GameArea/TopUI/ScoreLabel

var board_slots: Array[Button] = []
var player_score: int = 0
var enemy_score: int = 0

func _ready():
	setup_board()
	setup_game()

func _on_back_button_pressed():
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func setup_board():
	# Create 3x3 grid of slots
	for i in range(9):
		var slot = Button.new()
		slot.custom_minimum_size = Vector2(100, 120)
		slot.text = "Empty"
		slot.pressed.connect(_on_board_slot_pressed.bind(i))
		board.add_child(slot)
		board_slots.append(slot)

func setup_game():
	# TODO: Initialize player and enemy hands
	# TODO: Set up game rules and turn system
	print("Game setup - Triple Triad battle ready!")

func _on_board_slot_pressed(slot_index: int):
	print("Board slot ", slot_index, " pressed")
	# TODO: Handle card placement logic