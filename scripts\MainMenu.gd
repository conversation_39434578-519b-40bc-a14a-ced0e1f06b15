extends Control

@onready var title_label = $MainContainer/LeftPanel/TitleSection/TitleLabel
@onready var subtitle_label = $MainContainer/LeftPanel/TitleSection/SubtitleLabel
@onready var play_button = $MainContainer/LeftPanel/MenuSection/MenuButtons/PlayButton
@onready var collection_button = $MainContainer/LeftPanel/MenuSection/MenuButtons/CollectionButton
@onready var shop_button = $MainContainer/LeftPanel/MenuSection/MenuButtons/ShopButton
@onready var settings_button = $MainContainer/LeftPanel/MenuSection/MenuButtons/SettingsButton
@onready var quit_button = $MainContainer/LeftPanel/MenuSection/MenuButtons/QuitButton
@onready var background = $Background
@onready var card_showcase = $MainContainer/CardShowcase

func _ready():
	setup_simple_theme()
	create_ui_elements()
	animate_entrance()

func setup_simple_theme():
	# Simple dark background
	background.color = Color(0.1, 0.1, 0.2, 1.0)
	
	# Style buttons with simple colors
	setup_button_style(play_button, Color(0.2, 0.4, 0.8, 1.0))  # Blue
	setup_button_style(collection_button, Color(0.4, 0.4, 0.4, 1.0))  # Gray
	setup_button_style(shop_button, Color(0.2, 0.6, 0.2, 1.0))  # Green
	setup_button_style(settings_button, Color(0.4, 0.4, 0.4, 1.0))  # Gray
	setup_button_style(quit_button, Color(0.6, 0.2, 0.2, 1.0))  # Red

func setup_button_style(button: Button, color: Color):
	var style_normal = StyleBoxFlat.new()
	style_normal.bg_color = color
	style_normal.corner_radius_top_left = 8
	style_normal.corner_radius_top_right = 8
	style_normal.corner_radius_bottom_left = 8
	style_normal.corner_radius_bottom_right = 8
	
	var style_hover = StyleBoxFlat.new()
	style_hover.bg_color = Color(color.r * 1.2, color.g * 1.2, color.b * 1.2, 1.0)
	style_hover.corner_radius_top_left = 8
	style_hover.corner_radius_top_right = 8
	style_hover.corner_radius_bottom_left = 8
	style_hover.corner_radius_bottom_right = 8
	
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)

func create_ui_elements():
	# Create gold display at bottom right
	create_gold_display()
	
	# Create circular shop button at bottom left
	create_shop_button()

func create_gold_display():
	var gold_container = Panel.new()
	gold_container.size = Vector2(150, 50)
	gold_container.position = Vector2(get_viewport().size.x - 170, get_viewport().size.y - 70)
	
	# Style the gold container
	var gold_style = StyleBoxFlat.new()
	gold_style.bg_color = Color(0.15, 0.1, 0.05, 0.9)
	gold_style.corner_radius_top_left = 25
	gold_style.corner_radius_top_right = 25
	gold_style.corner_radius_bottom_left = 25
	gold_style.corner_radius_bottom_right = 25
	gold_style.border_width_left = 2
	gold_style.border_width_right = 2
	gold_style.border_width_top = 2
	gold_style.border_width_bottom = 2
	gold_style.border_color = Color(1.0, 0.8, 0.2)
	gold_container.add_theme_stylebox_override("panel", gold_style)
	
	# Gold label
	var gold_label = Label.new()
	gold_label.text = "💰 " + str(SaveSystem.player_gold)
	gold_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	gold_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	gold_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	gold_label.add_theme_font_size_override("font_size", 16)
	gold_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2))
	
	gold_container.add_child(gold_label)
	add_child(gold_container)

func create_shop_button():
	var shop_btn = Button.new()
	shop_btn.size = Vector2(80, 80)
	shop_btn.position = Vector2(20, get_viewport().size.y - 100)
	shop_btn.text = "🛒"
	shop_btn.flat = true
	
	# Create circular style
	var shop_style_normal = StyleBoxFlat.new()
	shop_style_normal.bg_color = Color(0.2, 0.6, 0.2, 0.9)
	shop_style_normal.corner_radius_top_left = 40
	shop_style_normal.corner_radius_top_right = 40
	shop_style_normal.corner_radius_bottom_left = 40
	shop_style_normal.corner_radius_bottom_right = 40
	shop_style_normal.border_width_left = 3
	shop_style_normal.border_width_right = 3
	shop_style_normal.border_width_top = 3
	shop_style_normal.border_width_bottom = 3
	shop_style_normal.border_color = Color(0.3, 0.8, 0.3)
	
	var shop_style_hover = StyleBoxFlat.new()
	shop_style_hover.bg_color = Color(0.3, 0.7, 0.3, 1.0)
	shop_style_hover.corner_radius_top_left = 40
	shop_style_hover.corner_radius_top_right = 40
	shop_style_hover.corner_radius_bottom_left = 40
	shop_style_hover.corner_radius_bottom_right = 40
	shop_style_hover.border_width_left = 3
	shop_style_hover.border_width_right = 3
	shop_style_hover.border_width_top = 3
	shop_style_hover.border_width_bottom = 3
	shop_style_hover.border_color = Color(0.4, 0.9, 0.4)
	shop_style_hover.shadow_color = Color(0.2, 0.6, 0.2, 0.6)
	shop_style_hover.shadow_size = 8
	
	shop_btn.add_theme_stylebox_override("normal", shop_style_normal)
	shop_btn.add_theme_stylebox_override("hover", shop_style_hover)
	shop_btn.add_theme_font_size_override("font_size", 32)
	
	shop_btn.pressed.connect(_on_circular_shop_button_pressed)
	add_child(shop_btn)
	
	# Create pack opening button next to shop
	create_pack_button()
	
	# Create debug button for testing
	create_debug_button()

func create_pack_button():
	var pack_btn = Button.new()
	pack_btn.size = Vector2(80, 80)
	pack_btn.position = Vector2(110, get_viewport().size.y - 100)  # Next to shop button
	pack_btn.text = "📦"
	pack_btn.flat = true
	
	# Create circular style with purple theme
	var pack_style_normal = StyleBoxFlat.new()
	pack_style_normal.bg_color = Color(0.6, 0.2, 0.8, 0.9)
	pack_style_normal.corner_radius_top_left = 40
	pack_style_normal.corner_radius_top_right = 40
	pack_style_normal.corner_radius_bottom_left = 40
	pack_style_normal.corner_radius_bottom_right = 40
	pack_style_normal.border_width_left = 3
	pack_style_normal.border_width_right = 3
	pack_style_normal.border_width_top = 3
	pack_style_normal.border_width_bottom = 3
	pack_style_normal.border_color = Color(0.8, 0.3, 0.9)
	
	var pack_style_hover = StyleBoxFlat.new()
	pack_style_hover.bg_color = Color(0.7, 0.3, 0.9, 1.0)
	pack_style_hover.corner_radius_top_left = 40
	pack_style_hover.corner_radius_top_right = 40
	pack_style_hover.corner_radius_bottom_left = 40
	pack_style_hover.corner_radius_bottom_right = 40
	pack_style_hover.border_width_left = 3
	pack_style_hover.border_width_right = 3
	pack_style_hover.border_width_top = 3
	pack_style_hover.border_width_bottom = 3
	pack_style_hover.border_color = Color(0.9, 0.4, 1.0)
	pack_style_hover.shadow_color = Color(0.6, 0.2, 0.8, 0.6)
	pack_style_hover.shadow_size = 8
	
	pack_btn.add_theme_stylebox_override("normal", pack_style_normal)
	pack_btn.add_theme_stylebox_override("hover", pack_style_hover)
	pack_btn.add_theme_font_size_override("font_size", 32)
	
	pack_btn.pressed.connect(_on_pack_button_pressed)
	add_child(pack_btn)
	
	# Add pack count pill
	var pack_count = SaveSystem.get_pack_count()
	if pack_count > 0:
		var pill = Panel.new()
		pill.size = Vector2(30, 20)
		pill.position = Vector2(165, get_viewport().size.y - 115)  # Top right of pack button
		
		var pill_style = StyleBoxFlat.new()
		pill_style.bg_color = Color(0.9, 0.2, 0.2, 0.95)
		pill_style.corner_radius_top_left = 10
		pill_style.corner_radius_top_right = 10
		pill_style.corner_radius_bottom_left = 10
		pill_style.corner_radius_bottom_right = 10
		pill_style.border_width_left = 1
		pill_style.border_width_right = 1
		pill_style.border_width_top = 1
		pill_style.border_width_bottom = 1
		pill_style.border_color = Color(1.0, 0.3, 0.3)
		pill.add_theme_stylebox_override("panel", pill_style)
		
		var count_label = Label.new()
		count_label.text = str(pack_count)
		count_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		count_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		count_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
		count_label.add_theme_font_size_override("font_size", 12)
		count_label.add_theme_color_override("font_color", Color.WHITE)
		
		pill.add_child(count_label)
		add_child(pill)

func _on_pack_button_pressed():
	if SaveSystem.get_pack_count() > 0:
		var tween = create_tween()
		tween.tween_property(self, "modulate:a", 0.0, 0.3)
		await tween.finished
		get_tree().change_scene_to_file("res://scenes/HearthstonePackOpening.tscn")
	else:
		print("No packs to open!")





func _on_circular_shop_button_pressed():
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	await tween.finished
	get_tree().change_scene_to_file("res://scenes/Shop.tscn")

func _on_showcase_card_selected(card: Card):
	# Handle showcase card selection - could show card details or collection
	print("Showcase card selected: ", card.name)
	# Could open card collection or show card details popup

func animate_entrance():
	# Simple fade-in animation
	title_label.modulate.a = 0
	subtitle_label.modulate.a = 0
	
	var title_tween = create_tween()
	title_tween.tween_property(title_label, "modulate:a", 1.0, 0.5)
	title_tween.tween_property(subtitle_label, "modulate:a", 1.0, 0.5)
	
	# Animate buttons
	for button in [play_button, collection_button, shop_button, settings_button, quit_button]:
		button.modulate.a = 0
	
	await title_tween.finished
	
	var button_tween = create_tween()
	button_tween.set_parallel(true)
	
	for i in range(5):
		var button = [play_button, collection_button, shop_button, settings_button, quit_button][i]
		button_tween.tween_property(button, "modulate:a", 1.0, 0.3).set_delay(i * 0.1)

func _on_showcase_card_hover(card: Panel, is_hovering: bool):
	var tween = create_tween()
	if is_hovering:
		tween.tween_property(card, "scale", Vector2(1.05, 1.05), 0.2)
	else:
		tween.tween_property(card, "scale", Vector2(1.0, 1.0), 0.2)

func _on_play_button_pressed():
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	await tween.finished
	get_tree().change_scene_to_file("res://scenes/GameBoard.tscn")

func _on_collection_button_pressed():
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	await tween.finished
	get_tree().change_scene_to_file("res://scenes/CardCollection.tscn")

func _on_shop_button_pressed():
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	await tween.finished
	get_tree().change_scene_to_file("res://scenes/Shop.tscn")

func _on_settings_button_pressed():
	# TODO: Load settings scene
	print("Settings button pressed - Settings scene not implemented yet")

func _on_quit_button_pressed():
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.5)
	await tween.finished
	get_tree().quit()

func create_debug_button():
	var debug_btn = Button.new()
	debug_btn.size = Vector2(60, 60)
	debug_btn.position = Vector2(200, get_viewport().size.y - 90)  # Next to pack button
	debug_btn.text = "💎"
	debug_btn.flat = true
	
	# Create circular style with orange theme for debug
	var debug_style_normal = StyleBoxFlat.new()
	debug_style_normal.bg_color = Color(0.8, 0.4, 0.0, 0.9)
	debug_style_normal.corner_radius_top_left = 30
	debug_style_normal.corner_radius_top_right = 30
	debug_style_normal.corner_radius_bottom_left = 30
	debug_style_normal.corner_radius_bottom_right = 30
	debug_style_normal.border_width_left = 2
	debug_style_normal.border_width_right = 2
	debug_style_normal.border_width_top = 2
	debug_style_normal.border_width_bottom = 2
	debug_style_normal.border_color = Color(1.0, 0.6, 0.2)
	
	var debug_style_hover = StyleBoxFlat.new()
	debug_style_hover.bg_color = Color(1.0, 0.5, 0.1, 1.0)
	debug_style_hover.corner_radius_top_left = 30
	debug_style_hover.corner_radius_top_right = 30
	debug_style_hover.corner_radius_bottom_left = 30
	debug_style_hover.corner_radius_bottom_right = 30
	debug_style_hover.border_width_left = 2
	debug_style_hover.border_width_right = 2
	debug_style_hover.border_width_top = 2
	debug_style_hover.border_width_bottom = 2
	debug_style_hover.border_color = Color(1.0, 0.7, 0.3)
	debug_style_hover.shadow_color = Color(0.8, 0.4, 0.0, 0.6)
	debug_style_hover.shadow_size = 6
	
	debug_btn.add_theme_stylebox_override("normal", debug_style_normal)
	debug_btn.add_theme_stylebox_override("hover", debug_style_hover)
	debug_btn.add_theme_font_size_override("font_size", 24)
	
	debug_btn.pressed.connect(_on_debug_button_pressed)
	add_child(debug_btn)

func _on_debug_button_pressed():
	# Add 500 gold for testing
	SaveSystem.player_gold += 500
	SaveSystem.save_game()
	
	# Update the gold display
	var gold_container = null
	for child in get_children():
		if child is Panel and child.size == Vector2(150, 50):
			gold_container = child
			break
	
	if gold_container:
		var gold_label = gold_container.get_child(0)
		gold_label.text = "💰 " + str(SaveSystem.player_gold)
	
	print("Debug: Added 500 gold! Total: ", SaveSystem.player_gold)
