extends Node

const SAVE_FILE_PATH = "user://game_save.dat"

var unlocked_cards: Dictionary = {}
var player_gold: int = 1000
var unopened_packs: Array[Dictionary] = []

func _ready():
	load_game()

# General save/load functions
func save_game(custom_data: Dictionary = {}) -> bool:
	var save_file = FileAccess.open(SAVE_FILE_PATH, FileAccess.WRITE)
	if save_file == null:
		print("Error: Could not create save file")
		return false
	
	var save_data = {
		"unlocked_cards": unlocked_cards,
		"gold": player_gold,
		"unopened_packs": unopened_packs,
		"version": "1.2"
	}
	
	# Merge any custom data
	for key in custom_data:
		save_data[key] = custom_data[key]
	
	save_file.store_string(JSON.stringify(save_data))
	save_file.close()
	print("Game saved successfully")
	return true

func load_game() -> Dictionary:
	if not FileAccess.file_exists(SAVE_FILE_PATH):
		print("No save file found, creating new game")
		initialize_new_game()
		return get_current_save_data()
	
	var save_file = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
	if save_file == null:
		print("Error: Could not open save file")
		initialize_new_game()
		return get_current_save_data()
	
	var json_string = save_file.get_as_text()
	save_file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		print("Error parsing save file, creating new game")
		initialize_new_game()
		return get_current_save_data()
	
	var save_data = json.data
	
	# Load unlocked cards
	if save_data.has("unlocked_cards"):
		unlocked_cards = save_data.unlocked_cards
		print("Collection loaded successfully: ", unlocked_cards.size(), " cards unlocked")
	else:
		initialize_starter_collection()
	
	# Load gold
	if save_data.has("gold"):
		player_gold = save_data.gold
		print("Gold loaded: ", player_gold)
	else:
		player_gold = 1000
	
	# Load unopened packs
	if save_data.has("unopened_packs"):
		unopened_packs.assign(save_data.unopened_packs)
		print("Packs loaded: ", unopened_packs.size(), " unopened packs")
	else:
		unopened_packs.clear()
	
	return save_data

func get_current_save_data() -> Dictionary:
	return {
		"unlocked_cards": unlocked_cards,
		"gold": player_gold,
		"version": "1.1"
	}

func initialize_new_game():
	initialize_starter_collection()
	player_gold = 1000
	save_game()

# Legacy function for backward compatibility
func save_collection():
	save_game()

# Legacy function for backward compatibility  
func load_collection():
	load_game()

func initialize_starter_collection():
	# Give player some starter cards from different factions and rarities
	unlocked_cards.clear()
	
	# Add some common cards from each faction as starters
	var starter_card_names = [
		"Solareon Squire",
		"Temple Shieldbearer", 
		"Dawnblade Initiate",
		"Ashclaw Cub",
		"Raid Scout",
		"Emberling",
		"Coven Novice",
		"Shadowling",
		"Gloom Sprite",
		"Iron Recruit",
		"Forge Apprentice",
		"Steel Initiate",
		"Void Wisp",
		"Shadow Minion",
		"Dark Familiar"
	]
	
	for card_name in starter_card_names:
		unlock_card(card_name)
	
	# Add one uncommon card as a starter bonus
	unlock_card("Solareon Knight")
	
	save_collection()

func is_card_unlocked(card_name: String) -> bool:
	return unlocked_cards.has(card_name)

func unlock_card(card_name: String) -> bool:
	if not unlocked_cards.has(card_name):
		unlocked_cards[card_name] = true
		print("Card unlocked: ", card_name)
		save_collection()
		return true
	return false

func lock_card(card_name: String):
	if unlocked_cards.has(card_name):
		unlocked_cards.erase(card_name)
		save_collection()

func get_unlocked_cards() -> Array[String]:
	return unlocked_cards.keys()

func get_unlocked_count() -> int:
	return unlocked_cards.size()

func get_unlocked_count_by_rarity(rarity: String, all_cards: Array[Card]) -> int:
	var count = 0
	for card in all_cards:
		if card.rarity == rarity and is_card_unlocked(card.name):
			count += 1
	return count

func get_total_count_by_rarity(rarity: String, all_cards: Array[Card]) -> int:
	var count = 0
	for card in all_cards:
		if card.rarity == rarity:
			count += 1
	return count

# Debug functions for testing
func unlock_all_cards(all_cards: Array[Card]):
	for card in all_cards:
		unlock_card(card.name)

func unlock_cards_by_rarity(rarity: String, all_cards: Array[Card]):
	for card in all_cards:
		if card.rarity == rarity:
			unlock_card(card.name)

func reset_collection():
	unlocked_cards.clear()
	save_collection()
	initialize_starter_collection()

# Pack management functions
func add_pack(pack_data: Dictionary):
	unopened_packs.append(pack_data)
	save_game()
	print("Pack added: ", pack_data.name)

func get_pack_count() -> int:
	return unopened_packs.size()

func open_pack() -> Dictionary:
	if unopened_packs.size() > 0:
		var pack = unopened_packs.pop_front()
		save_game()
		return pack
	return {}

func get_next_pack() -> Dictionary:
	if unopened_packs.size() > 0:
		return unopened_packs[0]
	return {}
