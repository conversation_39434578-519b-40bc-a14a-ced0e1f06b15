class_name Card
extends Resource

@export var name: String
@export var faction: String
@export var rarity: String
@export var hidden_tier: int
@export var affinity: String
@export var top: int
@export var right: int
@export var bottom: int
@export var left: int
@export var power_sum: int
@export var average_power: float
@export var image_path: String
@export var alt_arts: Array[String] = []
@export var card_set: String = "Base"

func _init(data: Dictionary = {}):
	if data.has("name"):
		name = data.name
		faction = data.faction
		rarity = data.rarity
		hidden_tier = data.hidden_tier
		affinity = data.affinity
		top = data.values.top
		right = data.values.right
		bottom = data.values.bottom
		left = data.values.left
		power_sum = data.power_sum
		average_power = data.average_power
		image_path = data.image_path
		if data.has("alt_arts"):
			alt_arts.assign(data.alt_arts)
		card_set = data.get("set", "Base")

func get_power_at_direction(direction: String) -> int:
	match direction:
		"top":
			return top
		"right":
			return right
		"bottom":
			return bottom
		"left":
			return left
		_:
			return 0

func get_faction_color() -> Color:
	match faction:
		"Knights of Solareon":
			return Color.GOLD
		"Ashclaw Raiders":
			return Color.ORANGE_RED
		"Eldergloom Coven":
			return Color.PURPLE
		"Iron Spire":
			return Color.GRAY
		"Voidbound":
			return Color.DARK_VIOLET
		"Neutral":
			return Color.WHITE
		_:
			return Color.WHITE

func get_rarity_color() -> Color:
	match rarity:
		"Common":
			return Color.WHITE
		"Uncommon":
			return Color.CYAN
		"Rare":
			return Color.GOLD
		"Legendary":
			return Color.MAGENTA
		_:
			return Color.WHITE
