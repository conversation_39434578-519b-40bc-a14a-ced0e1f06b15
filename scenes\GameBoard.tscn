[gd_scene load_steps=2 format=3 uid="uid://b8xlan4qwxpj"]

[ext_resource type="Script" uid="uid://dcttspsone8to" path="res://scripts/GameBoard.gd" id="1_0hdqx"]

[node name="GameBoard" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_0hdqx")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.15, 0.1, 1)

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="LeftPanel" type="VBoxContainer" parent="HBoxContainer"]
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 0

[node name="PlayerLabel" type="Label" parent="HBoxContainer/LeftPanel"]
layout_mode = 2
text = "Player Hand"
horizontal_alignment = 1

[node name="PlayerHand" type="VBoxContainer" parent="HBoxContainer/LeftPanel"]
layout_mode = 2
size_flags_vertical = 3

[node name="GameArea" type="VBoxContainer" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TopUI" type="HBoxContainer" parent="HBoxContainer/GameArea"]
layout_mode = 2

[node name="BackButton" type="Button" parent="HBoxContainer/GameArea/TopUI"]
layout_mode = 2
text = "← Back to Menu"

[node name="GameTitle" type="Label" parent="HBoxContainer/GameArea/TopUI"]
layout_mode = 2
size_flags_horizontal = 3
text = "Triple Triad Battle"
horizontal_alignment = 1

[node name="ScoreLabel" type="Label" parent="HBoxContainer/GameArea/TopUI"]
layout_mode = 2
text = "Player: 0 | Enemy: 0"

[node name="BoardContainer" type="CenterContainer" parent="HBoxContainer/GameArea"]
layout_mode = 2
size_flags_vertical = 3

[node name="Board" type="GridContainer" parent="HBoxContainer/GameArea/BoardContainer"]
layout_mode = 2
columns = 3

[node name="RightPanel" type="VBoxContainer" parent="HBoxContainer"]
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 0

[node name="EnemyLabel" type="Label" parent="HBoxContainer/RightPanel"]
layout_mode = 2
text = "Enemy Hand"
horizontal_alignment = 1

[node name="EnemyHand" type="VBoxContainer" parent="HBoxContainer/RightPanel"]
layout_mode = 2
size_flags_vertical = 3

[connection signal="pressed" from="HBoxContainer/GameArea/TopUI/BackButton" to="." method="_on_back_button_pressed"]
