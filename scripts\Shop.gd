extends Control

@onready var background = $Background
@onready var title_label = $MainContainer/HeaderSection/TitleContainer/TitleLabel
@onready var section_title = $MainContainer/ContentSection/SectionTitle
@onready var gold_label = $MainContainer/HeaderSection/GoldContainer/GoldLabel
@onready var back_button = $MainContainer/HeaderSection/BackButton
@onready var pack_container = $MainContainer/ContentSection/ScrollContainer/PackContainer
@onready var purchase_popup = $PurchasePopup
@onready var popup_background = $PurchasePopup/PopupBackground
@onready var popup_title = $PurchasePopup/PopupBackground/VBoxContainer/PopupTitle
@onready var popup_description = $PurchasePopup/PopupBackground/VBoxContainer/PopupDescription
@onready var popup_price = $PurchasePopup/PopupBackground/VBoxContainer/PopupPrice
@onready var confirm_button = $PurchasePopup/PopupBackground/VBoxContainer/ButtonContainer/ConfirmButton
@onready var cancel_button = $PurchasePopup/PopupBackground/VBoxContainer/ButtonContainer/CancelButton

var selected_pack = null

var pack_data = [
	{
		"name": "Starter Pack",
		"description": "5 cards guaranteed\n1 Rare or better",
		"price": 100,
		"color": Color(0.6, 0.6, 0.6),
		"cards": 5,
		"guaranteed_rare": true
	},
	{
		"name": "Mystic Pack",
		"description": "7 cards guaranteed\n1 Rare, chance for Epic",
		"price": 200,
		"color": Color(0.2, 0.8, 0.4),
		"cards": 7,
		"guaranteed_rare": true
	},
	{
		"name": "Legendary Pack",
		"description": "10 cards guaranteed\n1 Epic or Legendary",
		"price": 500,
		"color": Color(0.8, 0.6, 0.2),
		"cards": 10,
		"guaranteed_legendary": true
	},
	{
		"name": "Dragon's Hoard",
		"description": "15 premium cards\n3 Legendary guaranteed",
		"price": 1000,
		"color": Color(0.6, 0.2, 0.8),
		"cards": 15,
		"guaranteed_legendary": true,
		"premium": true
	}
]

func _ready():
	setup_simple_theme()
	load_player_data()
	create_pack_displays()
	animate_entrance()

func setup_simple_theme():
	# Simple dark background
	background.color = Color(0.1, 0.1, 0.2, 1.0)
	
	# Style back button
	setup_button_style(back_button, Color(0.4, 0.4, 0.4))
	
	# Style popup
	var popup_style = StyleBoxFlat.new()
	popup_style.bg_color = Color(0.2, 0.2, 0.3, 0.95)
	popup_style.corner_radius_top_left = 12
	popup_style.corner_radius_top_right = 12
	popup_style.corner_radius_bottom_left = 12
	popup_style.corner_radius_bottom_right = 12
	popup_style.border_width_left = 2
	popup_style.border_width_right = 2
	popup_style.border_width_top = 2
	popup_style.border_width_bottom = 2
	popup_style.border_color = Color(0.5, 0.5, 0.6)
	popup_background.add_theme_stylebox_override("panel", popup_style)
	
	# Style popup buttons
	setup_button_style(confirm_button, Color(0.2, 0.6, 0.2))
	setup_button_style(cancel_button, Color(0.6, 0.2, 0.2))

func setup_button_style(button: Button, color: Color):
	var style_normal = StyleBoxFlat.new()
	style_normal.bg_color = color
	style_normal.corner_radius_top_left = 8
	style_normal.corner_radius_top_right = 8
	style_normal.corner_radius_bottom_left = 8
	style_normal.corner_radius_bottom_right = 8
	
	var style_hover = StyleBoxFlat.new()
	style_hover.bg_color = Color(color.r * 1.2, color.g * 1.2, color.b * 1.2, 1.0)
	style_hover.corner_radius_top_left = 8
	style_hover.corner_radius_top_right = 8
	style_hover.corner_radius_bottom_left = 8
	style_hover.corner_radius_bottom_right = 8
	
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)

func load_player_data():
	# Load player's gold from save system
	SaveSystem.load_game()
	update_gold_display()

func update_gold_display():
	gold_label.text = "💰 " + str(SaveSystem.player_gold) + " Gold"

func create_pack_displays():
	for pack in pack_data:
		var pack_panel = create_pack_panel(pack)
		pack_container.add_child(pack_panel)

func create_pack_panel(pack_info: Dictionary) -> Panel:
	var panel = Panel.new()
	panel.custom_minimum_size = Vector2(280, 400)
	
	# Style panel with pack color
	var panel_style = StyleBoxFlat.new()
	panel_style.bg_color = Color(pack_info.color.r * 0.3, pack_info.color.g * 0.3, pack_info.color.b * 0.3, 0.9)
	panel_style.corner_radius_top_left = 12
	panel_style.corner_radius_top_right = 12
	panel_style.corner_radius_bottom_left = 12
	panel_style.corner_radius_bottom_right = 12
	panel_style.border_width_left = 3
	panel_style.border_width_right = 3
	panel_style.border_width_top = 3
	panel_style.border_width_bottom = 3
	panel_style.border_color = pack_info.color
	panel.add_theme_stylebox_override("panel", panel_style)
	
	# Pack image placeholder
	var image_container = Panel.new()
	image_container.size = Vector2(240, 180)
	image_container.position = Vector2(20, 20)
	
	var image_style = StyleBoxFlat.new()
	image_style.bg_color = Color(0.2, 0.2, 0.3, 1.0)
	image_style.corner_radius_top_left = 8
	image_style.corner_radius_top_right = 8
	image_style.corner_radius_bottom_left = 8
	image_style.corner_radius_bottom_right = 8
	image_container.add_theme_stylebox_override("panel", image_style)
	
	var image_label = Label.new()
	image_label.text = "🎴"
	image_label.size = image_container.size
	image_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	image_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	image_label.add_theme_font_size_override("font_size", 48)
	image_container.add_child(image_label)
	panel.add_child(image_container)
	
	# Pack name
	var name_label = Label.new()
	name_label.text = pack_info.name
	name_label.size = Vector2(240, 40)
	name_label.position = Vector2(20, 210)
	name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	name_label.add_theme_font_size_override("font_size", 18)
	name_label.add_theme_color_override("font_color", Color.WHITE)
	panel.add_child(name_label)
	
	# Pack description
	var desc_label = Label.new()
	desc_label.text = pack_info.description
	desc_label.size = Vector2(240, 80)
	desc_label.position = Vector2(20, 250)
	desc_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	desc_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	desc_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	desc_label.add_theme_font_size_override("font_size", 14)
	desc_label.add_theme_color_override("font_color", Color(0.9, 0.9, 0.9))
	panel.add_child(desc_label)
	
	# Price label
	var price_label = Label.new()
	price_label.text = "💰 " + str(pack_info.price) + " Gold"
	price_label.size = Vector2(240, 30)
	price_label.position = Vector2(20, 340)
	price_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	price_label.add_theme_font_size_override("font_size", 16)
	price_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2))
	panel.add_child(price_label)
	
	# Buy button
	var buy_button = Button.new()
	buy_button.text = "BUY PACK"
	buy_button.size = Vector2(200, 40)
	buy_button.position = Vector2(40, 350)
	buy_button.flat = true
	
	# Style button based on affordability
	if SaveSystem.player_gold >= pack_info.price:
		setup_button_style(buy_button, Color(0.2, 0.6, 0.2))
	else:
		setup_button_style(buy_button, Color(0.4, 0.4, 0.4))
		buy_button.disabled = true
	
	buy_button.pressed.connect(_on_pack_selected.bind(pack_info))
	panel.add_child(buy_button)
	
	# Add hover effects
	panel.mouse_entered.connect(_on_pack_hover.bind(panel, true))
	panel.mouse_exited.connect(_on_pack_hover.bind(panel, false))
	
	return panel

func animate_entrance():
	# Simple fade-in animation without position changes
	for i in range(pack_container.get_child_count()):
		var pack = pack_container.get_child(i)
		pack.modulate.a = 0
		
		var tween = create_tween()
		tween.tween_property(pack, "modulate:a", 1.0, 0.5).set_delay(i * 0.2)

func _on_pack_hover(pack: Panel, is_hovering: bool):
	var tween = create_tween()
	if is_hovering:
		tween.tween_property(pack, "scale", Vector2(1.05, 1.05), 0.2)
		tween.tween_property(pack, "modulate", Color(1.1, 1.1, 1.1, 1.0), 0.2)
	else:
		tween.tween_property(pack, "scale", Vector2(1.0, 1.0), 0.2)
		tween.tween_property(pack, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.2)

func _on_pack_selected(pack_info: Dictionary):
	if SaveSystem.player_gold < pack_info.price:
		return
	
	selected_pack = pack_info
	show_purchase_popup()

func show_purchase_popup():
	popup_title.text = "Purchase " + selected_pack.name + "?"
	popup_description.text = selected_pack.description
	popup_price.text = "Cost: 💰 " + str(selected_pack.price) + " Gold"
	
	purchase_popup.visible = true
	purchase_popup.modulate.a = 0
	
	var tween = create_tween()
	tween.tween_property(purchase_popup, "modulate:a", 1.0, 0.3)

func hide_purchase_popup():
	var tween = create_tween()
	tween.tween_property(purchase_popup, "modulate:a", 0.0, 0.3)
	await tween.finished
	purchase_popup.visible = false

func _on_confirm_button_pressed():
	if selected_pack and SaveSystem.player_gold >= selected_pack.price:
		# Deduct gold
		SaveSystem.player_gold -= selected_pack.price
		update_gold_display()
		
		# Add pack to inventory instead of opening immediately
		SaveSystem.add_pack(selected_pack)
		
		print("Purchased " + selected_pack.name + "! Added to your pack inventory.")
		
		# Refresh pack displays to update affordability
		for child in pack_container.get_children():
			child.queue_free()
		await get_tree().process_frame
		create_pack_displays()
		
		hide_purchase_popup()

func _on_cancel_button_pressed():
	hide_purchase_popup()

func _on_back_button_pressed():
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	await tween.finished
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")