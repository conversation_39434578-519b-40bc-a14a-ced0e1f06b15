[gd_scene load_steps=2 format=3 uid="uid://bvqxk8n7qwxpj"]

[ext_resource type="Script" uid="uid://dj6lwy3awj8k6" path="res://scripts/MainMenu.gd" id="1_0hdqx"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_0hdqx")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.2, 1)

[node name="MainContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = 50.0
offset_right = -50.0
offset_bottom = -50.0
grow_horizontal = 2
grow_vertical = 2

[node name="LeftPanel" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 2.0

[node name="TitleSection" type="VBoxContainer" parent="MainContainer/LeftPanel"]
layout_mode = 2
size_flags_vertical = 3

[node name="Spacer1" type="Control" parent="MainContainer/LeftPanel/TitleSection"]
layout_mode = 2
size_flags_vertical = 3

[node name="TitleLabel" type="Label" parent="MainContainer/LeftPanel/TitleSection"]
layout_mode = 2
text = "TRIPLE TRIAD ROGUE"
horizontal_alignment = 1

[node name="SubtitleLabel" type="Label" parent="MainContainer/LeftPanel/TitleSection"]
layout_mode = 2
text = "Master the Ancient Card Arts"
horizontal_alignment = 1

[node name="Spacer2" type="Control" parent="MainContainer/LeftPanel/TitleSection"]
layout_mode = 2
size_flags_vertical = 3

[node name="MenuSection" type="VBoxContainer" parent="MainContainer/LeftPanel"]
layout_mode = 2
size_flags_vertical = 3

[node name="MenuButtons" type="VBoxContainer" parent="MainContainer/LeftPanel/MenuSection"]
layout_mode = 2

[node name="PlayButton" type="Button" parent="MainContainer/LeftPanel/MenuSection/MenuButtons"]
layout_mode = 2
text = "Play"
flat = true

[node name="Spacer1" type="Control" parent="MainContainer/LeftPanel/MenuSection/MenuButtons"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="CollectionButton" type="Button" parent="MainContainer/LeftPanel/MenuSection/MenuButtons"]
layout_mode = 2
text = "Card Collection"
flat = true

[node name="Spacer2" type="Control" parent="MainContainer/LeftPanel/MenuSection/MenuButtons"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="ShopButton" type="Button" parent="MainContainer/LeftPanel/MenuSection/MenuButtons"]
layout_mode = 2
text = "Shop"
flat = true

[node name="Spacer3" type="Control" parent="MainContainer/LeftPanel/MenuSection/MenuButtons"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="SettingsButton" type="Button" parent="MainContainer/LeftPanel/MenuSection/MenuButtons"]
layout_mode = 2
text = "Settings"
flat = true

[node name="Spacer4" type="Control" parent="MainContainer/LeftPanel/MenuSection/MenuButtons"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="QuitButton" type="Button" parent="MainContainer/LeftPanel/MenuSection/MenuButtons"]
layout_mode = 2
text = "Quit"
flat = true

[node name="CardShowcase" type="HBoxContainer" parent="MainContainer"]
visible = false
layout_mode = 2
size_flags_horizontal = 3
alignment = 1

[connection signal="pressed" from="MainContainer/LeftPanel/MenuSection/MenuButtons/PlayButton" to="." method="_on_play_button_pressed"]
[connection signal="pressed" from="MainContainer/LeftPanel/MenuSection/MenuButtons/CollectionButton" to="." method="_on_collection_button_pressed"]
[connection signal="pressed" from="MainContainer/LeftPanel/MenuSection/MenuButtons/ShopButton" to="." method="_on_shop_button_pressed"]
[connection signal="pressed" from="MainContainer/LeftPanel/MenuSection/MenuButtons/SettingsButton" to="." method="_on_settings_button_pressed"]
[connection signal="pressed" from="MainContainer/LeftPanel/MenuSection/MenuButtons/QuitButton" to="." method="_on_quit_button_pressed"]
