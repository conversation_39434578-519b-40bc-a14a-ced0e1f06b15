extends Control

@onready var background = $Background
@onready var left_sidebar = $MainContainer/LeftSidebar
@onready var back_button = $MainContainer/LeftSidebar/SidebarContainer/HeaderContainer/BackButton
@onready var title_label = $MainContainer/LeftSidebar/SidebarContainer/TitleLabel
@onready var instruction_label = $MainContainer/LeftSidebar/SidebarContainer/InstructionLabel
@onready var pack_list = $MainContainer/LeftSidebar/SidebarContainer/PackScrollContainer/PackList
@onready var center_area = $MainContainer/CenterArea
@onready var drop_zone = $MainContainer/CenterArea/DropZone
@onready var drop_label = $MainContainer/CenterArea/DropZone/DropLabel
@onready var cards_area = $MainContainer/CenterArea/CardsArea
@onready var cards_container = $MainContainer/CenterArea/CardsArea/CardsScroll/CardsContainer

var pack_groups: Dictionary = {}
var current_pack: Dictionary = {}
var revealed_cards: Array[Card] = []
var face_down_cards: Array[Control] = []
var cards_revealed: int = 0

# --- GLOBAL DRAG STATE ---
var dragging_pack_container: Button = null
var dragging_pack_preview: Control = null
var dragging_pack_offset: Vector2 = Vector2.ZERO
var dragging_pack_data: Dictionary = {}

func _ready():
	setup_theme()
	load_pack_inventory()
	setup_drop_zone()

func setup_theme():
	# Dark fantasy background
	background.color = Color(0.08, 0.05, 0.15, 1.0)
	
	# Style sidebar
	var sidebar_style = StyleBoxFlat.new()
	sidebar_style.bg_color = Color(0.12, 0.08, 0.2, 0.9)
	sidebar_style.border_width_right = 2
	sidebar_style.border_color = Color(0.3, 0.2, 0.4, 1.0)
	left_sidebar.add_theme_stylebox_override("panel", sidebar_style)
	
	# Style buttons
	setup_button_style(back_button, Color(0.4, 0.4, 0.4))
	
	# Style labels
	title_label.add_theme_font_size_override("font_size", 18)
	title_label.add_theme_color_override("font_color", Color(0.9, 0.8, 1.0))
	
	instruction_label.add_theme_font_size_override("font_size", 12)
	instruction_label.add_theme_color_override("font_color", Color(0.7, 0.6, 0.8))

func setup_button_style(button: Button, color: Color):
	var style_normal = StyleBoxFlat.new()
	style_normal.bg_color = color
	style_normal.corner_radius_top_left = 8
	style_normal.corner_radius_top_right = 8
	style_normal.corner_radius_bottom_left = 8
	style_normal.corner_radius_bottom_right = 8
	
	var style_hover = StyleBoxFlat.new()
	style_hover.bg_color = Color(color.r * 1.2, color.g * 1.2, color.b * 1.2, 1.0)
	style_hover.corner_radius_top_left = 8
	style_hover.corner_radius_top_right = 8
	style_hover.corner_radius_bottom_left = 8
	style_hover.corner_radius_bottom_right = 8
	
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)

func setup_drop_zone():
	var drop_style = StyleBoxFlat.new()
	drop_style.bg_color = Color(0.2, 0.15, 0.3, 0.3)
	drop_style.corner_radius_top_left = 20
	drop_style.corner_radius_top_right = 20
	drop_style.corner_radius_bottom_left = 20
	drop_style.corner_radius_bottom_right = 20
	drop_style.border_width_left = 3
	drop_style.border_width_right = 3
	drop_style.border_width_top = 3
	drop_style.border_width_bottom = 3
	drop_style.border_color = Color(0.5, 0.4, 0.6, 0.8)
	drop_zone.add_theme_stylebox_override("panel", drop_style)
	
	drop_label.add_theme_font_size_override("font_size", 16)
	drop_label.add_theme_color_override("font_color", Color(0.8, 0.7, 0.9))

func load_pack_inventory():
	# Clear existing pack displays
	for child in pack_list.get_children():
		child.queue_free()
	
	# Group packs by name and count them
	pack_groups.clear()
	var available_packs = SaveSystem.unopened_packs
	
	for pack in available_packs:
		var pack_name = pack.name
		if not pack_groups.has(pack_name):
			pack_groups[pack_name] = {
				"pack_data": pack,
				"count": 0,
				"indices": []
			}
		pack_groups[pack_name].count += 1
		pack_groups[pack_name].indices.append(available_packs.find(pack))
	
	# Create pack displays for each group
	for pack_name in pack_groups.keys():
		var pack_group = pack_groups[pack_name]
		create_pack_display(pack_name, pack_group)

func create_pack_display(pack_name: String, pack_group: Dictionary):
	var pack_container = create_draggable_pack(pack_name, pack_group)
	pack_list.add_child(pack_container)

func create_draggable_pack(pack_name: String, pack_group: Dictionary) -> Control:
	# Use Button as base instead of Control for better input handling
	var pack_container = Button.new()
	pack_container.custom_minimum_size = Vector2(260, 80)
	pack_container.flat = true  # Make it look like a panel
	
	print("DEBUG: Created pack container (Button) for: ", pack_name)
	
	# Get pack color
	var pack_color = get_pack_color_for_pack(pack_group.pack_data)
	
	# Style the button itself
	var pack_style = StyleBoxFlat.new()
	pack_style.bg_color = Color(pack_color.r * 0.3, pack_color.g * 0.3, pack_color.b * 0.3, 0.9)
	pack_style.corner_radius_top_left = 12
	pack_style.corner_radius_top_right = 12
	pack_style.corner_radius_bottom_left = 12
	pack_style.corner_radius_bottom_right = 12
	pack_style.border_width_left = 2
	pack_style.border_width_right = 2
	pack_style.border_width_top = 2
	pack_style.border_width_bottom = 2
	pack_style.border_color = pack_color
	pack_container.add_theme_stylebox_override("normal", pack_style)
	pack_container.add_theme_stylebox_override("hover", pack_style)
	pack_container.add_theme_stylebox_override("pressed", pack_style)
	
	# Content container
	var content_container = HBoxContainer.new()
	content_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	content_container.offset_left = 10
	content_container.offset_right = -10
	content_container.offset_top = 10
	content_container.offset_bottom = -10
	# IMPORTANT: Make sure container doesn't block input
	content_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	pack_container.add_child(content_container)
	
	# Pack icon
	var pack_icon = Label.new()
	pack_icon.text = "📦"
	pack_icon.custom_minimum_size = Vector2(60, 60)
	pack_icon.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	pack_icon.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	pack_icon.add_theme_font_size_override("font_size", 40)
	pack_icon.modulate = pack_color
	# IMPORTANT: Make sure icon doesn't block input
	pack_icon.mouse_filter = Control.MOUSE_FILTER_IGNORE
	content_container.add_child(pack_icon)
	
	# Pack info container
	var info_container = VBoxContainer.new()
	info_container.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	# IMPORTANT: Make sure info container doesn't block input
	info_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	content_container.add_child(info_container)
	
	# Pack name
	var name_label = Label.new()
	name_label.text = pack_name
	name_label.add_theme_font_size_override("font_size", 14)
	name_label.add_theme_color_override("font_color", Color.WHITE)
	name_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	# IMPORTANT: Make sure label doesn't block input
	name_label.mouse_filter = Control.MOUSE_FILTER_IGNORE
	info_container.add_child(name_label)
	
	# Pack details
	var details_label = Label.new()
	details_label.text = str(pack_group.pack_data.cards) + " cards per pack"
	details_label.add_theme_font_size_override("font_size", 10)
	details_label.add_theme_color_override("font_color", Color(0.8, 0.8, 0.8))
	# IMPORTANT: Make sure label doesn't block input
	details_label.mouse_filter = Control.MOUSE_FILTER_IGNORE
	info_container.add_child(details_label)
	
	# Count badge
	var count_badge = Label.new()
	count_badge.text = "x" + str(pack_group.count)
	count_badge.custom_minimum_size = Vector2(40, 30)
	count_badge.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	count_badge.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	count_badge.add_theme_font_size_override("font_size", 16)
	count_badge.add_theme_color_override("font_color", pack_color)
	# IMPORTANT: Make sure badge doesn't block input
	count_badge.mouse_filter = Control.MOUSE_FILTER_IGNORE
	content_container.add_child(count_badge)
	
	# Make it draggable
	setup_drag_and_drop(pack_container, pack_name, pack_group)
	
	return pack_container

func setup_drag_and_drop(pack_container: Button, pack_name: String, pack_group: Dictionary):
	# Store drag data in the pack container for access in callbacks
	pack_container.set_meta("pack_name", pack_name)
	pack_container.set_meta("pack_group", pack_group)
	pack_container.set_meta("is_dragging", false)
	pack_container.set_meta("drag_offset", Vector2.ZERO)
	pack_container.set_meta("drag_preview", null)

	# Use Button's built-in signals instead of gui_input
	pack_container.button_down.connect(_on_pack_button_down.bind(pack_container))
	pack_container.button_up.connect(_on_pack_button_up.bind(pack_container))
	# Remove gui_input connection for drag, will use _input instead
	# pack_container.gui_input.connect(_on_pack_mouse_motion.bind(pack_container))

	print("DEBUG: Connected button signals for pack: ", pack_name)
	print("DEBUG: Pack container mouse_filter: ", pack_container.mouse_filter)
	print("DEBUG: Pack container size: ", pack_container.custom_minimum_size)

func _on_pack_button_down(pack_container: Button):
	print("DEBUG: Pack button down for: ", pack_container.get_meta("pack_name"))

	var pack_name = pack_container.get_meta("pack_name")
	var pack_group = pack_container.get_meta("pack_group")

	# Start dragging (global)
	pack_container.set_meta("is_dragging", true)
	pack_container.set_meta("drag_offset", Vector2(50, 40))  # Center of pack

	dragging_pack_container = pack_container
	dragging_pack_data = {"pack_name": pack_name, "pack_group": pack_group}
	dragging_pack_offset = Vector2(50, 40)

	print("DEBUG: Creating drag preview...")
	# Create drag preview
	var drag_preview = create_drag_preview(pack_name, pack_group)
	get_tree().current_scene.add_child(drag_preview)
	drag_preview.global_position = pack_container.global_position
	pack_container.set_meta("drag_preview", drag_preview)
	dragging_pack_preview = drag_preview

	print("DEBUG: Highlighting drop zone...")
	# Highlight drop zone
	highlight_drop_zone(true)
	print("DEBUG: Drag started successfully!")

func _on_pack_button_up(pack_container: Button):
	print("DEBUG: Pack button up for: ", pack_container.get_meta("pack_name"))

	var is_dragging = pack_container.get_meta("is_dragging")
	if not is_dragging:
		return

	var pack_name = pack_container.get_meta("pack_name")
	var pack_group = pack_container.get_meta("pack_group")
	var drag_preview = pack_container.get_meta("drag_preview")

	print("DEBUG: Stopping drag...")
	pack_container.set_meta("is_dragging", false)

	# Check if dropped on drop zone using current mouse position
	var mouse_pos = get_global_mouse_position()
	var drop_zone_rect = Rect2(drop_zone.global_position, drop_zone.size)
	print("DEBUG: Drop zone rect: ", drop_zone_rect, ", mouse pos: ", mouse_pos)

	if drop_zone_rect.has_point(mouse_pos):
		print("DEBUG: Successfully dropped on drop zone! Opening pack...")
		# Successfully dropped on drop zone
		open_pack(pack_name, pack_group)
	else:
		print("DEBUG: Dropped outside drop zone")

	# Clean up
	if drag_preview:
		print("DEBUG: Cleaning up drag preview...")
		drag_preview.queue_free()
		pack_container.set_meta("drag_preview", null)
	if dragging_pack_preview:
		dragging_pack_preview.queue_free()
		dragging_pack_preview = null
	dragging_pack_container = null
	dragging_pack_data = {}
	dragging_pack_offset = Vector2.ZERO

	highlight_drop_zone(false)

# Remove _on_pack_mouse_motion, will use _input instead
# func _on_pack_mouse_motion(pack_container: Button, event: InputEvent):
# 	if event is InputEventMouseMotion:
# 		var is_dragging = pack_container.get_meta("is_dragging")
# 		var drag_preview = pack_container.get_meta("drag_preview")
# 		var drag_offset = pack_container.get_meta("drag_offset")
# 		if is_dragging and drag_preview:
# 			# Update drag preview position
# 			drag_preview.global_position = event.global_position - drag_offset

func _input(event):
	if dragging_pack_container and dragging_pack_container.get_meta("is_dragging"):
		if event is InputEventMouseMotion:
			if dragging_pack_preview:
				dragging_pack_preview.global_position = event.global_position - dragging_pack_offset
		elif event is InputEventMouseButton and not event.pressed:
			# Mouse button released anywhere, end drag
			_on_pack_button_up(dragging_pack_container)


func create_drag_preview(pack_name: String, pack_group: Dictionary) -> Control:
	var preview = Control.new()
	preview.custom_minimum_size = Vector2(200, 60)
	preview.modulate.a = 0.8
	
	var preview_panel = Panel.new()
	preview_panel.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	preview.add_child(preview_panel)
	
	# Style similar to original but slightly transparent
	var pack_color = get_pack_color_for_pack(pack_group.pack_data)
	var preview_style = StyleBoxFlat.new()
	preview_style.bg_color = Color(pack_color.r * 0.4, pack_color.g * 0.4, pack_color.b * 0.4, 0.8)
	preview_style.corner_radius_top_left = 12
	preview_style.corner_radius_top_right = 12
	preview_style.corner_radius_bottom_left = 12
	preview_style.corner_radius_bottom_right = 12
	preview_style.border_width_left = 2
	preview_style.border_width_right = 2
	preview_style.border_width_top = 2
	preview_style.border_width_bottom = 2
	preview_style.border_color = pack_color
	preview_panel.add_theme_stylebox_override("panel", preview_style)
	
	# Add pack name
	var name_label = Label.new()
	name_label.text = pack_name
	name_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	name_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	name_label.add_theme_color_override("font_color", Color.WHITE)
	preview_panel.add_child(name_label)
	
	return preview

func highlight_drop_zone(highlight: bool):
	if highlight:
		var highlight_style = StyleBoxFlat.new()
		highlight_style.bg_color = Color(0.3, 0.6, 0.3, 0.5)
		highlight_style.corner_radius_top_left = 20
		highlight_style.corner_radius_top_right = 20
		highlight_style.corner_radius_bottom_left = 20
		highlight_style.corner_radius_bottom_right = 20
		highlight_style.border_width_left = 4
		highlight_style.border_width_right = 4
		highlight_style.border_width_top = 4
		highlight_style.border_width_bottom = 4
		highlight_style.border_color = Color(0.5, 0.8, 0.5, 1.0)
		drop_zone.add_theme_stylebox_override("panel", highlight_style)
		drop_label.text = "Release to open pack!"
	else:
		setup_drop_zone()  # Reset to original style
		drop_label.text = "Drop pack here to open"

func get_pack_color_for_pack(pack: Dictionary) -> Color:
	if pack.has("color"):
		var color_value = pack.color
		# Handle different color formats
		if color_value is Color:
			return color_value
		elif color_value is Array and color_value.size() >= 3:
			return Color(color_value[0], color_value[1], color_value[2], color_value.get(3, 1.0))
		elif color_value is Dictionary:
			return Color(color_value.get("r", 0.7), color_value.get("g", 0.7), color_value.get("b", 0.7), color_value.get("a", 1.0))
	
	# Default colors based on pack name/type
	if "Legendary" in pack.name or "Dragon" in pack.name:
		return Color(0.9, 0.2, 0.9)  # Purple
	elif "Rare" in pack.name or "Mystic" in pack.name:
		return Color(1.0, 0.8, 0.0)  # Gold
	else:
		return Color(0.7, 0.7, 0.7)  # Silver
func open_pack(pack_name: String, pack_group: Dictionary):
	# Remove one pack from inventory
	var pack_index = pack_group.indices[0]  # Take the first available pack
	current_pack = SaveSystem.unopened_packs[pack_index].duplicate()
	SaveSystem.unopened_packs.remove_at(pack_index)
	SaveSystem.save_game()
	
	# Update pack group count
	pack_group.count -= 1
	pack_group.indices.remove_at(0)
	
	# Refresh pack inventory display
	load_pack_inventory()
	
	# Generate cards for this pack
	generate_pack_cards()
	
	# Hide drop zone and show pack explosion animation
	animate_pack_explosion()

func generate_pack_cards():
	revealed_cards.clear()
	var all_cards = CardManager.get_all_cards()
	
	# Generate cards based on pack guarantees
	var cards_to_generate = current_pack.cards
	var guaranteed_rare = current_pack.get("guaranteed_rare", false)
	var guaranteed_legendary = current_pack.get("guaranteed_legendary", false)
	
	# Add guaranteed cards first
	if guaranteed_legendary:
		var legendary_cards = CardManager.get_cards_by_rarity("Legendary")
		if legendary_cards.size() > 0:
			revealed_cards.append(legendary_cards[randi() % legendary_cards.size()])
			cards_to_generate -= 1
	elif guaranteed_rare:
		var rare_cards = CardManager.get_cards_by_rarity("Rare")
		if rare_cards.size() > 0:
			revealed_cards.append(rare_cards[randi() % rare_cards.size()])
			cards_to_generate -= 1
	
	# Fill remaining slots with random cards
	for i in range(cards_to_generate):
		var random_card = all_cards[randi() % all_cards.size()]
		revealed_cards.append(random_card)
	
	# Unlock all revealed cards
	for card in revealed_cards:
		SaveSystem.unlock_card(card.name)

func animate_pack_explosion():
	# Create dramatic pack explosion effect
	await create_pack_explosion_effect()

	# Hide drop zone
	drop_zone.visible = false

	# Show cards area
	cards_area.visible = true

	# Create face-down cards
	create_face_down_cards()

	# Animate cards appearing
	animate_cards_appearing()

func create_pack_explosion_effect():
	# Get pack rarity for effect intensity
	var pack_rarity = get_pack_rarity_from_current_pack()
	var pack_color = get_pack_color_for_pack(current_pack)

	# Play explosion sound effect
	play_sound_effect("pack_explosion", pack_rarity)

	# Create explosion overlay with background dimming
	var explosion_overlay = Control.new()
	explosion_overlay.name = "PackExplosionOverlay"
	explosion_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	explosion_overlay.mouse_filter = Control.MOUSE_FILTER_IGNORE
	explosion_overlay.z_index = 100
	add_child(explosion_overlay)

	# Add subtle background dimming
	var dim_background = ColorRect.new()
	dim_background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	dim_background.color = Color(0, 0, 0, 0.3)
	dim_background.modulate.a = 0.0
	explosion_overlay.add_child(dim_background)

	# Animate background dimming
	var dim_tween = create_tween()
	dim_tween.tween_property(dim_background, "modulate:a", 1.0, 0.3)
	dim_tween.tween_property(dim_background, "modulate:a", 0.0, 0.5).set_delay(1.0)

	# Add dramatic text overlay
	create_explosion_text_overlay(explosion_overlay, pack_rarity)

	# Create central explosion burst
	var burst_center = Vector2(get_viewport().size.x / 2, get_viewport().size.y / 2)
	await create_explosion_burst(explosion_overlay, burst_center, pack_color, pack_rarity)

	# Add screen shake effect
	create_screen_shake_effect()

	# Create particle effects based on pack rarity
	create_rarity_particle_effects(explosion_overlay, pack_rarity, pack_color)

	# Wait for explosion to settle
	await get_tree().create_timer(1.5).timeout

	# Clean up explosion overlay
	explosion_overlay.queue_free()

func create_explosion_text_overlay(parent: Control, rarity: String):
	# Create dramatic text that appears during explosion
	var text_label = Label.new()
	text_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	text_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	text_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	text_label.modulate.a = 0.0

	# Set text based on rarity
	match rarity:
		"Legendary":
			text_label.text = "✨ LEGENDARY PACK! ✨"
			text_label.add_theme_color_override("font_color", Color(0.9, 0.2, 0.9))
		"Rare":
			text_label.text = "⭐ RARE PACK! ⭐"
			text_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.0))
		_:
			text_label.text = "📦 PACK OPENING! 📦"
			text_label.add_theme_color_override("font_color", Color(0.8, 0.8, 0.9))

	# Make text larger and bold
	text_label.add_theme_font_size_override("font_size", 32)

	parent.add_child(text_label)

	# Animate text appearance
	var text_tween = create_tween()
	text_tween.set_parallel(true)

	# Fade in with scale
	text_tween.tween_property(text_label, "modulate:a", 1.0, 0.3)
	text_tween.tween_property(text_label, "scale", Vector2(1.2, 1.2), 0.3)

	# Fade out after delay
	text_tween.tween_property(text_label, "modulate:a", 0.0, 0.4).set_delay(1.1)
	text_tween.tween_property(text_label, "scale", Vector2(0.8, 0.8), 0.4).set_delay(1.1)

func play_sound_effect(effect_type: String, rarity: String = "Common"):
	# Placeholder for sound effects - in a real implementation, you would:
	# 1. Load appropriate sound files
	# 2. Create AudioStreamPlayer nodes
	# 3. Play different sounds based on effect_type and rarity

	print("🔊 Playing sound effect: ", effect_type, " (", rarity, " rarity)")

	# Example of what you might implement:
	# match effect_type:
	#     "pack_explosion":
	#         match rarity:
	#             "Legendary":
	#                 # Play epic explosion sound
	#                 pass
	#             "Rare":
	#                 # Play magical explosion sound
	#                 pass
	#             _:
	#                 # Play basic explosion sound
	#                 pass
	#     "card_reveal":
	#         # Play card flip sound with rarity-based pitch/effects
	#         pass

func get_pack_rarity_from_current_pack() -> String:
	# Determine pack rarity based on guaranteed cards or pack name
	if current_pack.get("guaranteed_legendary", false):
		return "Legendary"
	elif current_pack.get("guaranteed_rare", false):
		return "Rare"
	elif "Legendary" in current_pack.name or "Dragon" in current_pack.name:
		return "Legendary"
	elif "Rare" in current_pack.name or "Mystic" in current_pack.name:
		return "Rare"
	else:
		return "Common"

func create_explosion_burst(parent: Control, center: Vector2, color: Color, rarity: String):
	# Create multiple expanding rings for the burst effect
	var ring_count = 3 if rarity == "Legendary" else 2

	for i in range(ring_count):
		var ring = ColorRect.new()
		ring.size = Vector2(50, 50)
		ring.position = center - (ring.size * 0.5)
		ring.color = Color(color.r, color.g, color.b, 0.6 - i * 0.2)

		# Make ring circular
		var style = StyleBoxFlat.new()
		style.bg_color = ring.color
		style.corner_radius_top_left = 25
		style.corner_radius_top_right = 25
		style.corner_radius_bottom_left = 25
		style.corner_radius_bottom_right = 25
		ring.add_theme_stylebox_override("panel", style)

		parent.add_child(ring)

		# Animate ring expansion
		var tween = create_tween()
		tween.set_parallel(true)

		var final_size = Vector2(400 + i * 100, 400 + i * 100)
		tween.tween_property(ring, "size", final_size, 0.8 + i * 0.2)
		tween.tween_property(ring, "position", center - (final_size * 0.5), 0.8 + i * 0.2)
		tween.tween_property(ring, "modulate:a", 0.0, 0.8 + i * 0.2)

		# Clean up ring after animation
		tween.tween_callback(func(): ring.queue_free()).set_delay(0.8 + i * 0.2)

func create_screen_shake_effect():
	# Create subtle screen shake by moving the main container
	var main_container = $MainContainer
	var original_position = main_container.position
	var shake_intensity = 8.0
	var shake_duration = 0.6

	var shake_tween = create_tween()
	shake_tween.set_loops(int(shake_duration * 20))  # 20 shakes per second

	for i in range(int(shake_duration * 20)):
		var shake_offset = Vector2(
			randf_range(-shake_intensity, shake_intensity),
			randf_range(-shake_intensity, shake_intensity)
		)
		shake_tween.tween_property(main_container, "position", original_position + shake_offset, 0.05)

	# Return to original position
	shake_tween.tween_property(main_container, "position", original_position, 0.1)

func create_rarity_particle_effects(parent: Control, rarity: String, color: Color):
	# Create different particle effects based on rarity
	match rarity:
		"Common":
			create_simple_sparkles(parent, color, 8)
		"Rare":
			create_simple_sparkles(parent, color, 15)
			create_golden_embers(parent)
		"Legendary":
			create_simple_sparkles(parent, color, 25)
			create_golden_embers(parent)
			create_mystical_aura(parent, color)
			create_floating_orbs(parent, color)

func create_simple_sparkles(parent: Control, color: Color, count: int):
	var viewport_size = get_viewport().size

	for i in range(count):
		var sparkle = ColorRect.new()
		sparkle.size = Vector2(6, 6)
		sparkle.color = Color(color.r, color.g, color.b, 0.8)
		sparkle.position = Vector2(
			randf() * viewport_size.x,
			randf() * viewport_size.y
		)
		sparkle.rotation = PI / 4  # Diamond shape

		parent.add_child(sparkle)

		# Animate sparkle
		var tween = create_tween()
		tween.set_parallel(true)

		# Float and fade
		tween.tween_property(sparkle, "position:y", sparkle.position.y - 200, 2.0 + randf())
		tween.tween_property(sparkle, "modulate:a", 0.0, 1.5 + randf())
		tween.tween_property(sparkle, "rotation", sparkle.rotation + PI * 2, 1.0 + randf())

		# Clean up
		tween.tween_callback(func(): sparkle.queue_free()).set_delay(2.5)

func create_golden_embers(parent: Control):
	var viewport_size = get_viewport().size

	for i in range(12):
		var ember = ColorRect.new()
		ember.size = Vector2(4, 4)
		ember.color = Color(1.0, 0.6, 0.2, 0.7)
		ember.position = Vector2(
			randf() * viewport_size.x,
			viewport_size.y + 20  # Start below screen
		)

		parent.add_child(ember)

		# Animate ember floating upward
		var tween = create_tween()
		tween.set_parallel(true)

		tween.tween_property(ember, "position:y", -50, 3.0 + randf() * 2.0)
		tween.tween_property(ember, "position:x", ember.position.x + randf_range(-100, 100), 3.0 + randf() * 2.0)
		tween.tween_property(ember, "modulate:a", 0.0, 2.0 + randf())

		# Clean up
		tween.tween_callback(func(): ember.queue_free()).set_delay(5.0)

func create_mystical_aura(parent: Control, color: Color):
	var viewport_size = get_viewport().size
	var center = viewport_size * 0.5

	for i in range(3):
		var aura = ColorRect.new()
		var aura_size = Vector2(100 + i * 50, 100 + i * 50)
		aura.size = aura_size
		aura.position = center - (aura_size * 0.5)
		aura.color = Color(color.r, color.g, color.b, 0.1 + i * 0.05)

		# Make aura circular
		var style = StyleBoxFlat.new()
		style.bg_color = aura.color
		var radius = aura_size.x / 2
		style.corner_radius_top_left = radius
		style.corner_radius_top_right = radius
		style.corner_radius_bottom_left = radius
		style.corner_radius_bottom_right = radius
		aura.add_theme_stylebox_override("panel", style)

		parent.add_child(aura)

		# Animate pulsing aura
		var tween = create_tween()
		tween.set_loops()
		tween.tween_property(aura, "modulate:a", 0.3, 1.5 + i * 0.3)
		tween.tween_property(aura, "modulate:a", 0.05, 1.5 + i * 0.3)

		# Clean up after 3 seconds
		var cleanup_timer = Timer.new()
		cleanup_timer.wait_time = 3.0
		cleanup_timer.one_shot = true
		cleanup_timer.timeout.connect(func():
			aura.queue_free()
			cleanup_timer.queue_free()
		)
		add_child(cleanup_timer)
		cleanup_timer.start()

func create_floating_orbs(parent: Control, color: Color):
	var viewport_size = get_viewport().size

	for i in range(6):
		var orb = Panel.new()
		orb.size = Vector2(12, 12)
		orb.position = Vector2(
			randf() * viewport_size.x,
			randf() * viewport_size.y
		)

		# Make orb circular with glow
		var style = StyleBoxFlat.new()
		style.bg_color = Color(color.r, color.g, color.b, 0.8)
		style.corner_radius_top_left = 6
		style.corner_radius_top_right = 6
		style.corner_radius_bottom_left = 6
		style.corner_radius_bottom_right = 6
		orb.add_theme_stylebox_override("panel", style)

		parent.add_child(orb)

		# Animate floating motion
		var tween = create_tween()
		tween.set_parallel(true)

		# Circular floating motion
		var center = orb.position
		var radius = 50.0
		var duration = 4.0 + randf() * 2.0

		for j in range(int(duration * 10)):  # 10 steps per second
			var angle = (j / (duration * 10.0)) * PI * 2
			var target_pos = center + Vector2(cos(angle), sin(angle)) * radius
			tween.tween_property(orb, "position", target_pos, 0.1)

		# Fade out
		tween.tween_property(orb, "modulate:a", 0.0, 1.0).set_delay(duration - 1.0)

		# Clean up
		tween.tween_callback(func(): orb.queue_free()).set_delay(duration)

func create_face_down_cards():
	# Clear existing cards
	for child in cards_container.get_children():
		child.queue_free()

	face_down_cards.clear()
	cards_revealed = 0

	# Set up grid columns based on number of cards
	var card_count = revealed_cards.size()
	if card_count <= 5:
		cards_container.columns = card_count
	else:
		cards_container.columns = 5  # 5 cards per row for larger packs

	# Create face-down card displays
	for i in range(card_count):
		var face_down_card = create_face_down_card(i)
		cards_container.add_child(face_down_card)
		face_down_cards.append(face_down_card)

func create_face_down_card(index: int) -> Control:
	var card_container = Control.new()
	card_container.custom_minimum_size = Vector2(160, 160) # Square aspect ratio
	card_container.modulate.a = 0  # Start invisible for animation

	# Card back panel
	var card_back = Panel.new()
	card_back.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	card_container.add_child(card_back)

	# Style the card back
	var back_style = StyleBoxFlat.new()
	back_style.bg_color = Color(0.2, 0.1, 0.4, 1.0)
	back_style.corner_radius_top_left = 12
	back_style.corner_radius_top_right = 12
	back_style.corner_radius_bottom_left = 12
	back_style.corner_radius_bottom_right = 12
	back_style.border_width_left = 3
	back_style.border_width_right = 3
	back_style.border_width_top = 3
	back_style.border_width_bottom = 3
	back_style.border_color = Color(0.6, 0.4, 0.8, 1.0)
	card_back.add_theme_stylebox_override("panel", back_style)

	# Card back design
	var back_design = Label.new()
	back_design.text = "🎴"
	back_design.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	back_design.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	back_design.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	back_design.add_theme_font_size_override("font_size", 60)
	back_design.modulate = Color(0.8, 0.6, 1.0, 0.8)
	card_back.add_child(back_design)

	# Click to reveal text
	var click_label = Label.new()
	click_label.text = "Click to reveal"
	click_label.position = Vector2(10, 180)
	click_label.size = Vector2(140, 30)
	click_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	click_label.add_theme_font_size_override("font_size", 10)
	click_label.add_theme_color_override("font_color", Color(0.9, 0.8, 1.0))
	card_back.add_child(click_label)

	# Make clickable
	var click_button = Button.new()
	click_button.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	click_button.flat = true
	click_button.modulate.a = 0  # Invisible but clickable
	card_container.add_child(click_button)

	# Connect click to reveal
	click_button.pressed.connect(_on_card_clicked.bind(index))

	return card_container

func animate_cards_appearing():
	# Animate each card appearing with a stagger
	for i in range(face_down_cards.size()):
		var card = face_down_cards[i]
		var tween = create_tween()
		
		# Start from center and scale up
		card.scale = Vector2(0.1, 0.1)
		card.modulate.a = 0
		
		tween.parallel().tween_property(card, "scale", Vector2(1.0, 1.0), 0.5).set_delay(i * 0.1)
		tween.parallel().tween_property(card, "modulate:a", 1.0, 0.3).set_delay(i * 0.1)
		
		# Add a slight bounce effect
		tween.tween_property(card, "scale", Vector2(1.1, 1.1), 0.1).set_delay(i * 0.1 + 0.5)
		tween.tween_property(card, "scale", Vector2(1.0, 1.0), 0.1)

func _on_card_clicked(card_index: int):
	if card_index >= revealed_cards.size():
		return
	
	var card_container = face_down_cards[card_index]
	var card = revealed_cards[card_index]
	
	# Animate card flip
	animate_card_reveal(card_container, card, card_index)

func animate_card_reveal(card_container: Control, card: Card, _card_index: int):
	# Create dramatic card reveal effect
	await create_card_reveal_effect(card_container, card)

	# Set pivot to center for proper rotation
	card_container.pivot_offset = card_container.size / 2

	# Create realistic card flip animation
	var tween = create_tween()
	tween.set_parallel(true)

	# First half of flip - rotate to 90 degrees (edge view) and scale down horizontally
	tween.tween_property(card_container, "rotation_degrees", 90, 0.15).set_ease(Tween.EASE_IN)
	tween.tween_property(card_container, "scale:x", 0.1, 0.15).set_ease(Tween.EASE_IN)

	# Wait for first half to complete
	await tween.finished

	# Replace face-down card with actual card display at the midpoint
	replace_with_revealed_card(card_container, card)

	# Second half of flip - rotate back to 0 degrees and scale back up
	var flip_back_tween = create_tween()
	flip_back_tween.set_parallel(true)
	flip_back_tween.tween_property(card_container, "rotation_degrees", 0, 0.15).set_ease(Tween.EASE_OUT)
	flip_back_tween.tween_property(card_container, "scale:x", 1.0, 0.15).set_ease(Tween.EASE_OUT)

	# Add slight bounce effect at the end
	await flip_back_tween.finished
	var bounce_tween = create_tween()
	bounce_tween.tween_property(card_container, "scale", Vector2(1.05, 1.05), 0.1).set_ease(Tween.EASE_OUT)
	bounce_tween.tween_property(card_container, "scale", Vector2(1.0, 1.0), 0.1).set_ease(Tween.EASE_IN)

	# Add enhanced glow effect for rare cards
	add_enhanced_rarity_glow(card_container, card)

	cards_revealed += 1

	# Check if all cards are revealed
	if cards_revealed >= revealed_cards.size():
		await get_tree().create_timer(1.0).timeout
		show_completion_options()

func create_card_reveal_effect(card_container: Control, card: Card):
	# Play card reveal sound
	play_sound_effect("card_reveal", card.rarity)

	# Create a brief flash effect before revealing the card
	var flash_overlay = ColorRect.new()
	flash_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	flash_overlay.color = Color.WHITE
	flash_overlay.modulate.a = 0.0
	flash_overlay.mouse_filter = Control.MOUSE_FILTER_IGNORE
	card_container.add_child(flash_overlay)

	# Flash effect based on card rarity
	var flash_intensity = 0.3
	var flash_duration = 0.1

	match card.rarity:
		"Rare":
			flash_intensity = 0.6
			flash_duration = 0.15
		"Legendary":
			flash_intensity = 0.9
			flash_duration = 0.2

	# Animate flash
	var flash_tween = create_tween()
	flash_tween.tween_property(flash_overlay, "modulate:a", flash_intensity, flash_duration / 2)
	flash_tween.tween_property(flash_overlay, "modulate:a", 0.0, flash_duration / 2)

	await flash_tween.finished
	flash_overlay.queue_free()

func add_enhanced_rarity_glow(card_container: Control, card: Card):
	var rarity_color = get_rarity_color(card.rarity)

	# Add glow for all cards, but make it more intense for rare cards
	var glow_effect = ColorRect.new()
	glow_effect.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	glow_effect.color = Color(rarity_color.r, rarity_color.g, rarity_color.b, 0.2)
	glow_effect.mouse_filter = Control.MOUSE_FILTER_IGNORE

	# Insert behind the card
	card_container.add_child(glow_effect)
	card_container.move_child(glow_effect, 0)

	# Enhanced effects for rare+ cards
	if card.rarity in ["Rare", "Legendary"]:
		# Create pulsing glow
		var glow_tween = create_tween()
		glow_tween.set_loops()
		glow_tween.tween_property(glow_effect, "modulate:a", 0.8, 1.0)
		glow_tween.tween_property(glow_effect, "modulate:a", 0.3, 1.0)

		# Add particle burst for legendary cards
		if card.rarity == "Legendary":
			create_legendary_card_burst(card_container, rarity_color)

func create_legendary_card_burst(card_container: Control, color: Color):
	# Create small particle burst around the card
	var card_center = card_container.size * 0.5

	for i in range(8):
		var particle = ColorRect.new()
		particle.size = Vector2(3, 3)
		particle.color = Color(color.r, color.g, color.b, 0.8)
		particle.position = card_center - (particle.size * 0.5)

		card_container.add_child(particle)

		# Animate particles radiating outward
		var angle = (i / 8.0) * PI * 2
		var target_pos = card_center + Vector2(cos(angle), sin(angle)) * 60

		var particle_tween = create_tween()
		particle_tween.set_parallel(true)
		particle_tween.tween_property(particle, "position", target_pos, 0.5)
		particle_tween.tween_property(particle, "modulate:a", 0.0, 0.5)

		# Clean up
		particle_tween.tween_callback(func(): particle.queue_free()).set_delay(0.5)

func replace_with_revealed_card(card_container: Control, card: Card):
	# Clear existing children
	for child in card_container.get_children():
		child.queue_free()

	# Use actual CardDisplay scene for proper rendering
	var card_display_scene = preload("res://scenes/CardDisplay.tscn")
	var card_display = card_display_scene.instantiate()

	# Set correct aspect ratio and size (square)
	card_display.custom_minimum_size = Vector2(160, 160)
	card_display.position = Vector2(0, 0)

	card_container.add_child(card_display)
	card_display.setup_card(card)

	# Disable mouse interactions
	disable_mouse_recursively(card_display)

func add_rarity_glow(card_container: Control, card: Card):
	var rarity_color = get_rarity_color(card.rarity)
	
	# Only add glow for rare+ cards
	if card.rarity in ["Rare", "Legendary"]:
		var glow_effect = ColorRect.new()
		glow_effect.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		glow_effect.color = Color(rarity_color.r, rarity_color.g, rarity_color.b, 0.3)
		glow_effect.mouse_filter = Control.MOUSE_FILTER_IGNORE
		
		# Insert behind the card
		card_container.add_child(glow_effect)
		card_container.move_child(glow_effect, 0)
		
		# Animate glow
		var glow_tween = create_tween()
		glow_tween.set_loops()
		glow_tween.tween_property(glow_effect, "modulate:a", 0.8, 1.0)
		glow_tween.tween_property(glow_effect, "modulate:a", 0.3, 1.0)

func get_rarity_color(rarity: String) -> Color:
	match rarity:
		"Common":
			return Color(0.7, 0.7, 0.7)
		"Uncommon":
			return Color(0.2, 0.8, 0.3)
		"Rare":
			return Color(1.0, 0.8, 0.0)
		"Legendary":
			return Color(0.9, 0.2, 0.9)
		_:
			return Color.WHITE

func show_completion_options():
	# Create completion overlay
	var completion_overlay = Control.new()
	completion_overlay.name = "CompletionOverlay"
	completion_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	completion_overlay.mouse_filter = Control.MOUSE_FILTER_STOP
	add_child(completion_overlay)
	
	# Semi-transparent background
	var bg = ColorRect.new()
	bg.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	bg.color = Color(0, 0, 0, 0.7)
	completion_overlay.add_child(bg)
	
	# Center container
	var center_container = CenterContainer.new()
	center_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	completion_overlay.add_child(center_container)
	
	# Button container
	var button_container = VBoxContainer.new()
	button_container.add_theme_constant_override("separation", 20)
	center_container.add_child(button_container)
	
	# Title
	var title = Label.new()
	title.text = "Pack Opened!"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.add_theme_font_size_override("font_size", 24)
	title.add_theme_color_override("font_color", Color.WHITE)
	button_container.add_child(title)
	
	# Open another pack button (if available)
	if SaveSystem.get_pack_count() > 0:
		var open_another_btn = Button.new()
		open_another_btn.text = "OPEN ANOTHER PACK"
		open_another_btn.custom_minimum_size = Vector2(200, 50)
		open_another_btn.flat = true
		setup_button_style(open_another_btn, Color(0.2, 0.6, 0.8))
		button_container.add_child(open_another_btn)
		
		open_another_btn.pressed.connect(_on_open_another_pack.bind(completion_overlay))
	
	# Back to menu button
	var back_btn = Button.new()
	back_btn.text = "BACK TO MENU"
	back_btn.custom_minimum_size = Vector2(200, 50)
	back_btn.flat = true
	setup_button_style(back_btn, Color(0.6, 0.2, 0.2))
	button_container.add_child(back_btn)
	
	back_btn.pressed.connect(_on_back_button_pressed)

func _on_open_another_pack(overlay: Control):
	# Remove completion overlay
	overlay.queue_free()
	
	# Reset the scene for another pack opening
	reset_for_new_pack()

func reset_for_new_pack():
	# Hide cards area and show drop zone
	cards_area.visible = false
	drop_zone.visible = true
	
	# Clear cards
	for child in cards_container.get_children():
		child.queue_free()
	
	face_down_cards.clear()
	revealed_cards.clear()
	current_pack.clear()
	cards_revealed = 0
	
	# Reload pack inventory
	load_pack_inventory()

func disable_mouse_recursively(node: Node):
	if node is Control:
		node.mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	for child in node.get_children():
		disable_mouse_recursively(child)

func _on_back_button_pressed():
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	await tween.finished
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")
