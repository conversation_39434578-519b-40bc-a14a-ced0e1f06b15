extends CanvasLayer

signal transition_finished

var transition_tween: Tween

func _ready():
	# Start hidden
	modulate = Color(1, 1, 1, 0)

func fade_to_scene(scene_path: String):
	# Create magical fade effect
	show_magical_transition()
	
	# Wait for transition, then change scene
	await transition_finished
	get_tree().change_scene_to_file(scene_path)
	
	# Fade out transition
	hide_magical_transition()

func show_magical_transition():
	# Create magical overlay
	var overlay = ColorRect.new()
	overlay.name = "MagicalOverlay"
	overlay.color = FantasyTheme.COLORS.deep_void
	overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(overlay)
	
	# Add mystical particles
	var particle_container = Control.new()
	particle_container.name = "TransitionParticles"
	particle_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	particle_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	overlay.add_child(particle_container)
	
	# Create swirling magical effect
	FantasyTheme.create_particle_system(particle_container, "mystical_aura")
	FantasyTheme.create_particle_system(particle_container, "sparkles")
	
	# Add loading text
	var loading_label = Label.new()
	loading_label.text = "✨ Weaving Magic... ✨"
	loading_label.label_settings = FantasyTheme.create_fantasy_label("title")
	loading_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	loading_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	loading_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	overlay.add_child(loading_label)
	
	# Animate fade in
	overlay.modulate = Color(1, 1, 1, 0)
	transition_tween = create_tween()
	transition_tween.tween_property(overlay, "modulate", Color(1, 1, 1, 1), 0.5)
	transition_tween.tween_delay(1.0)  # Show effect for 1 second
	transition_tween.tween_callback(func(): transition_finished.emit())

func hide_magical_transition():
	var overlay = find_child("MagicalOverlay")
	if overlay:
		var fade_tween = create_tween()
		fade_tween.tween_property(overlay, "modulate", Color(1, 1, 1, 0), 0.5)
		fade_tween.tween_callback(func(): overlay.queue_free())