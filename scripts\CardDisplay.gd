extends Control

@onready var card_name = $CardArtArea/NameBox/CardName
@onready var top_label = $CardArtArea/PowerNumbers/Top
@onready var right_label = $CardArtArea/PowerNumbers/Right
@onready var bottom_label = $CardArtArea/PowerNumbers/Bottom
@onready var left_label = $CardArtArea/PowerNumbers/Left
@onready var name_box = $CardArtArea/NameBox
@onready var card_button = $CardButton
@onready var card_frame = $CardFrame

var card_data: Card
var hover_tween: Tween
var original_scale: Vector2
var original_position: Vector2
var is_hovered: bool = false

signal card_selected(card: Card)

func setup_card(card: Card):
	card_data = card
	
	# If nodes aren't ready yet, wait for them
	if not is_node_ready():
		await ready
	
	var is_unlocked = SaveSystem.is_card_unlocked(card.name)
	
	# Set card information based on unlock status
	if is_unlocked:
		# Show full card details
		if card_name:
			card_name.text = card.name
		if top_label:
			top_label.text = str(card.top)
		if right_label:
			right_label.text = str(card.right)
		if bottom_label:
			bottom_label.text = str(card.bottom)
		if left_label:
			left_label.text = str(card.left)
		
		# Load card image if it exists
		load_card_image(card.image_path)
	else:
		# Show placeholder for locked cards
		if card_name:
			card_name.text = "???"
		if top_label:
			top_label.text = "?"
		if right_label:
			right_label.text = "?"
		if bottom_label:
			bottom_label.text = "?"
		if left_label:
			left_label.text = "?"
		
		# Load placeholder image
		load_placeholder_image()
	
	# Style the power numbers with stroke
	style_power_numbers(is_unlocked)
	
	# Style the name box
	style_name_box(is_unlocked)

func style_power_numbers(is_unlocked: bool = true):
	# Apply stroke/outline styling to all power number labels
	var power_labels = [top_label, right_label, bottom_label, left_label]
	
	for label in power_labels:
		if not label:
			continue
		
		# Create a LabelSettings resource for the stroke effect
		var label_settings = LabelSettings.new()
		
		# Set font size for better visibility
		label_settings.font_size = 18
		
		if is_unlocked:
			# Set text color to white for good contrast
			label_settings.font_color = Color.WHITE
			
			# Add black outline/stroke
			label_settings.outline_size = 3
			label_settings.outline_color = Color.BLACK
			
			# Apply shadow for extra depth
			label_settings.shadow_size = 2
			label_settings.shadow_color = Color(0, 0, 0, 0.7)
			label_settings.shadow_offset = Vector2(1, 1)
		else:
			# Dimmed styling for locked cards
			label_settings.font_color = Color(0.5, 0.5, 0.5, 0.8)
			label_settings.outline_size = 2
			label_settings.outline_color = Color(0.2, 0.2, 0.2, 0.8)
			label_settings.shadow_size = 1
			label_settings.shadow_color = Color(0, 0, 0, 0.5)
			label_settings.shadow_offset = Vector2(1, 1)
		
		# Apply the label settings
		label.label_settings = label_settings

func style_name_box(is_unlocked: bool = true):
	if not name_box or not card_data:
		return
	
	# Create a StyleBoxFlat for the name box
	var style_box = StyleBoxFlat.new()
	
	if is_unlocked:
		# Set background color based on rarity with transparency
		match card_data.rarity:
			"Common":
				style_box.bg_color = Color(0.2, 0.2, 0.2, 0.85)  # Dark gray with transparency
				style_box.border_color = Color(0.7, 0.7, 0.7, 1.0)  # Silver border
			"Uncommon":
				style_box.bg_color = Color(0.1, 0.3, 0.1, 0.85)  # Dark green with transparency
				style_box.border_color = Color(0.2, 0.8, 0.3, 1.0)  # Green border
			"Rare":
				style_box.bg_color = Color(0.3, 0.2, 0.0, 0.85)  # Dark gold with transparency
				style_box.border_color = Color(1.0, 0.8, 0.0, 1.0)  # Gold border
			"Legendary":
				style_box.bg_color = Color(0.2, 0.1, 0.3, 0.85)  # Dark purple with transparency
				style_box.border_color = Color(0.8, 0.2, 0.8, 1.0)  # Magenta border
	else:
		# Locked card styling - dark and muted
		style_box.bg_color = Color(0.1, 0.1, 0.1, 0.9)  # Very dark background
		style_box.border_color = Color(0.3, 0.3, 0.3, 0.8)  # Dark gray border
	
	# Set border properties
	style_box.border_width_left = 1
	style_box.border_width_right = 1
	style_box.border_width_top = 1
	style_box.border_width_bottom = 1
	
	# Set corner radius for rounded corners
	style_box.corner_radius_top_left = 4
	style_box.corner_radius_top_right = 4
	style_box.corner_radius_bottom_left = 4
	style_box.corner_radius_bottom_right = 4
	
	# Apply the style to the name box
	name_box.add_theme_stylebox_override("panel", style_box)

func clear_existing_card_images(card_art_area: Control):
	# Remove ALL children that could be images or placeholders
	var children_to_remove = []
	for child in card_art_area.get_children():
		if child.name == "CardImage" or child is TextureRect or child is ColorRect:
			children_to_remove.append(child)
	
	# Remove them immediately
	for child in children_to_remove:
		card_art_area.remove_child(child)
		child.queue_free()

func load_card_image(image_path: String):
	if image_path.is_empty():
		return
	
	var full_path = "res://" + image_path
	var texture: Texture2D = null
	
	# Method 1: Try loading the misnamed JPEG files
	if FileAccess.file_exists(full_path):
		var image = Image.new()
		var file_bytes = FileAccess.get_file_as_bytes(full_path)
		if file_bytes.size() > 0:
			# Since the files are JPEG with .png extension, try JPEG loading first
			var error = image.load_jpg_from_buffer(file_bytes)
			if error == OK:
				texture = ImageTexture.new()
				texture.set_image(image)
			else:
				# Try PNG if JPEG fails
				error = image.load_png_from_buffer(file_bytes)
				if error == OK:
					texture = ImageTexture.new()
					texture.set_image(image)
	
	# Method 2: Try ResourceLoader if ImageTexture failed
	if not texture and ResourceLoader.exists(full_path):
		texture = ResourceLoader.load(full_path) as Texture2D
	
	# Fallback to placeholder if original fails
	if not texture:
		var placeholder_path = "res://data/card_images/placeholder.png"
		if FileAccess.file_exists(placeholder_path):
			var image = Image.new()
			if image.load(placeholder_path) == OK:
				texture = ImageTexture.new()
				texture.set_image(image)
		
		# If ImageTexture placeholder fails, try ResourceLoader
		if not texture and ResourceLoader.exists(placeholder_path):
			texture = ResourceLoader.load(placeholder_path) as Texture2D
	
	if texture != null:
		# Get the card art area to add the image to
		var card_art_area = $CardArtArea
		if not card_art_area:
			return
		
		# Remove existing image from card art area immediately
		clear_existing_card_images(card_art_area)
		
		# Create a TextureRect that fills only the card art area
		var image_rect = TextureRect.new()
		image_rect.name = "CardImage"
		image_rect.texture = texture
		image_rect.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
		image_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
		image_rect.mouse_filter = Control.MOUSE_FILTER_IGNORE
		
		# Make it fill only the card art area
		image_rect.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		
		# Add it to the card art area as the first child so it appears behind everything
		card_art_area.add_child(image_rect)
		card_art_area.move_child(image_rect, 0)
		
		# Add rarity border effects to the card art area only
		create_rarity_border()

func load_placeholder_image():
	# Get the card art area to add the placeholder to
	var card_art_area = $CardArtArea
	if not card_art_area:
		return
	
	# Remove existing image from card art area immediately
	clear_existing_card_images(card_art_area)
	
	# Create a dark placeholder with question mark or lock icon
	var placeholder = ColorRect.new()
	placeholder.name = "CardImage"
	placeholder.color = Color(0.15, 0.15, 0.15, 0.9)  # Dark gray
	placeholder.mouse_filter = Control.MOUSE_FILTER_IGNORE
	placeholder.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Add a lock symbol or question mark in the center
	var lock_label = Label.new()
	lock_label.text = "🔒"  # Lock emoji, or use "?" if emoji doesn't work
	lock_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	lock_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	lock_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	lock_label.mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	# Style the lock label
	var label_settings = LabelSettings.new()
	label_settings.font_size = 48
	label_settings.font_color = Color(0.4, 0.4, 0.4, 0.8)
	lock_label.label_settings = label_settings
	
	placeholder.add_child(lock_label)
	
	# Add it to the card art area as the first child so it appears behind everything
	card_art_area.add_child(placeholder)
	card_art_area.move_child(placeholder, 0)
	
	# Add muted border for locked cards
	create_locked_border()

func create_rarity_border():
	if not card_data:
		return
	
	# Get the card art area to apply borders to
	var card_art_area = $CardArtArea
	if not card_art_area:
		return
	
	# Remove existing border
	var existing_border = card_art_area.find_child("RarityBorder")
	if existing_border:
		existing_border.queue_free()
	
	# Create border container for the card art area only
	var border_container = Control.new()
	border_container.name = "RarityBorder"
	border_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	border_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	# Add border container to the card art area
	card_art_area.add_child(border_container)
	
	match card_data.rarity:
		"Common":
			create_common_border(border_container)
		"Uncommon":
			create_uncommon_border(border_container)
		"Rare":
			create_rare_border(border_container)
		"Legendary":
			create_legendary_border(border_container)

func create_border_frame(container: Control, color: Color, thickness: int):
	# Create 4 separate rectangles for top, right, bottom, left borders
	# This creates a frame around the edges without covering the center
	
	# Top border
	var top_border = ColorRect.new()
	top_border.color = color
	top_border.mouse_filter = Control.MOUSE_FILTER_IGNORE
	top_border.set_anchors_and_offsets_preset(Control.PRESET_TOP_WIDE)
	top_border.set_offset(SIDE_BOTTOM, thickness)
	container.add_child(top_border)
	
	# Bottom border
	var bottom_border = ColorRect.new()
	bottom_border.color = color
	bottom_border.mouse_filter = Control.MOUSE_FILTER_IGNORE
	bottom_border.set_anchors_and_offsets_preset(Control.PRESET_BOTTOM_WIDE)
	bottom_border.set_offset(SIDE_TOP, -thickness)
	container.add_child(bottom_border)
	
	# Left border
	var left_border = ColorRect.new()
	left_border.color = color
	left_border.mouse_filter = Control.MOUSE_FILTER_IGNORE
	left_border.set_anchors_and_offsets_preset(Control.PRESET_LEFT_WIDE)
	left_border.set_offset(SIDE_RIGHT, thickness)
	container.add_child(left_border)
	
	# Right border
	var right_border = ColorRect.new()
	right_border.color = color
	right_border.mouse_filter = Control.MOUSE_FILTER_IGNORE
	right_border.set_anchors_and_offsets_preset(Control.PRESET_RIGHT_WIDE)
	right_border.set_offset(SIDE_LEFT, -thickness)
	container.add_child(right_border)

func create_common_border(container: Control):
	# Simple silver border frame for common cards
	create_border_frame(container, Color(0.7, 0.7, 0.7, 0.9), 3)

func create_uncommon_border(container: Control):
	# Green glowing double border frame for uncommon cards
	create_border_frame(container, Color(0.2, 0.8, 0.3, 0.9), 4)
	create_border_frame(container, Color(0.1, 0.6, 0.2, 0.7), 2)

func create_rare_border(container: Control):
	# Golden animated triple border frame for rare cards
	var outer_frame = create_border_frame(container, Color(1.0, 0.8, 0.0, 0.95), 5)
	var middle_frame = create_border_frame(container, Color(0.8, 0.6, 0.0, 0.8), 3)
	var inner_frame = create_border_frame(container, Color(1.0, 0.9, 0.2, 0.6), 1)
	
	# Add pulsing animation to the outer frame
	var outer_children = []
	for child in container.get_children():
		if child.color.is_equal_approx(Color(1.0, 0.8, 0.0, 0.95)):
			outer_children.append(child)
	
	for border_rect in outer_children:
		var tween = create_tween()
		tween.set_loops()
		tween.tween_property(border_rect, "modulate", Color(1.2, 1.0, 0.4, 0.95), 1.0)
		tween.tween_property(border_rect, "modulate", Color(1.0, 0.8, 0.0, 0.95), 1.0)

func create_legendary_border(container: Control):
	# Epic multi-layered animated border frame for legendary cards
	create_border_frame(container, Color(0.8, 0.2, 0.8, 0.98), 6)  # Outer magenta
	create_border_frame(container, Color(0.6, 0.1, 0.9, 0.9), 4)   # Middle purple
	create_border_frame(container, Color(1.0, 0.4, 1.0, 0.8), 2)   # Inner bright magenta
	create_border_frame(container, Color(0.9, 0.6, 1.0, 0.6), 1)   # Glow effect
	
	# Add complex pulsing animation to different layers
	var outer_children = []
	var glow_children = []
	
	for child in container.get_children():
		if child.color.is_equal_approx(Color(0.8, 0.2, 0.8, 0.98)):
			outer_children.append(child)
		elif child.color.is_equal_approx(Color(0.9, 0.6, 1.0, 0.6)):
			glow_children.append(child)
	
	# Animate outer border
	for border_rect in outer_children:
		var tween1 = create_tween()
		tween1.set_loops()
		tween1.tween_property(border_rect, "modulate", Color(1.0, 0.4, 1.0, 0.98), 0.8)
		tween1.tween_property(border_rect, "modulate", Color(0.8, 0.2, 0.8, 0.98), 0.8)
	
	# Animate glow border
	for border_rect in glow_children:
		var tween2 = create_tween()
		tween2.set_loops()
		tween2.tween_property(border_rect, "modulate", Color(1.2, 0.8, 1.2, 0.8), 1.2)
		tween2.tween_property(border_rect, "modulate", Color(0.9, 0.6, 1.0, 0.4), 1.2)

func create_locked_border():
	# Get the card art area to apply borders to
	var card_art_area = $CardArtArea
	if not card_art_area:
		return
	
	# Remove existing border
	var existing_border = card_art_area.find_child("RarityBorder")
	if existing_border:
		existing_border.queue_free()
	
	# Create border container for the card art area only
	var border_container = Control.new()
	border_container.name = "RarityBorder"
	border_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	border_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	# Add border container to the card art area
	card_art_area.add_child(border_container)
	
	# Simple dark border for locked cards
	create_border_frame(border_container, Color(0.3, 0.3, 0.3, 0.6), 2)

func _ready():
	# Store original transform values
	original_scale = scale
	original_position = position

	# Ensure CardFrame is at the bottom
	if card_frame:
		move_child(card_frame, 0)

	# Remove extreme border from CardFrame, use subtle background only
	if card_frame:
		var frame_style = StyleBoxFlat.new()
		frame_style.bg_color = Color(0.12, 0.12, 0.18, 1.0)
		frame_style.border_color = Color(0, 0, 0, 0)
		frame_style.border_width_left = 0
		frame_style.border_width_right = 0
		frame_style.border_width_top = 0
		frame_style.border_width_bottom = 0
		frame_style.corner_radius_top_left = 16
		frame_style.corner_radius_top_right = 16
		frame_style.corner_radius_bottom_left = 16
		frame_style.corner_radius_bottom_right = 16
		card_frame.add_theme_stylebox_override("panel", frame_style)

	# Add a thick, white, rounded border to CardArtArea
	var card_art_area = $CardArtArea
	if card_art_area:
		card_art_area.clip_contents = true
		var art_style = StyleBoxFlat.new()
		art_style.bg_color = Color(0.18, 0.18, 0.22, 1.0)
		art_style.border_color = Color(1, 1, 1, 1)
		art_style.border_width_left = 8
		art_style.border_width_right = 8
		art_style.border_width_top = 8
		art_style.border_width_bottom = 8
		art_style.corner_radius_top_left = 32
		art_style.corner_radius_top_right = 32
		art_style.corner_radius_bottom_left = 32
		art_style.corner_radius_bottom_right = 32
		card_art_area.add_theme_stylebox_override("panel", art_style)

	# Connect hover signals
	if card_button:
		card_button.mouse_entered.connect(_on_card_mouse_entered)
		card_button.mouse_exited.connect(_on_card_mouse_exited)

func _on_card_button_pressed():
	card_selected.emit(card_data)

func _on_card_mouse_entered():
	if card_data:
		is_hovered = true
		animate_hover_in()

func _on_card_mouse_exited():
	is_hovered = false
	animate_hover_out()

func animate_hover_in():
	# Stop any existing tween
	if hover_tween:
		hover_tween.kill()
	
	# Set pivot to center for proper scaling
	pivot_offset = size / 2
	
	# Create smooth hover animation
	hover_tween = create_tween()
	hover_tween.set_parallel(true)
	
	# Scale up slightly - no position changes to avoid grid issues
	var target_scale = original_scale * 1.08
	
	hover_tween.tween_property(self, "scale", target_scale, 0.2).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)
	
	# Add subtle glow effect based on rarity
	add_hover_glow_effect()

func animate_hover_out():
	# Stop any existing tween
	if hover_tween:
		hover_tween.kill()
	
	# Return to original state
	hover_tween = create_tween()
	hover_tween.set_parallel(true)
	
	hover_tween.tween_property(self, "scale", original_scale, 0.15).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_QUART)
	
	# Remove glow effect
	remove_hover_glow_effect()

func add_hover_glow_effect():
	if not card_data:
		return
	
	# Remove existing glow
	var existing_glow = find_child("HoverGlow")
	if existing_glow:
		existing_glow.queue_free()
	
	# Create glow effect based on rarity
	var glow = ColorRect.new()
	glow.name = "HoverGlow"
	glow.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	glow.mouse_filter = Control.MOUSE_FILTER_IGNORE
	glow.z_index = -1
	
	# Set glow color based on rarity
	match card_data.rarity:
		"Common":
			glow.color = Color(0.8, 0.8, 0.9, 0.3)
		"Uncommon":
			glow.color = Color(0.2, 0.8, 0.3, 0.4)
		"Rare":
			glow.color = Color(1.0, 0.8, 0.0, 0.5)
		"Legendary":
			glow.color = Color(0.9, 0.2, 0.9, 0.6)
	
	add_child(glow)
	
	# Animate glow
	glow.modulate = Color(1, 1, 1, 0)
	var glow_tween = create_tween()
	glow_tween.tween_property(glow, "modulate", Color(1, 1, 1, 1), 0.2)

func remove_hover_glow_effect():
	var glow = find_child("HoverGlow")
	if glow:
		var fade_tween = create_tween()
		fade_tween.tween_property(glow, "modulate", Color(1, 1, 1, 0), 0.15)
		fade_tween.tween_callback(func(): glow.queue_free()).set_delay(0.15)
