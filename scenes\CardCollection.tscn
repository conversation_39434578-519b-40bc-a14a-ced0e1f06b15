[gd_scene load_steps=2 format=3 uid="uid://c8xk7n2qwxpj"]

[ext_resource type="Script" uid="uid://c1qwoe0m6im5h" path="res://scripts/CardCollection.gd" id="1_0hdqx"]

[node name="CardCollection" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_0hdqx")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.15, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Header" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="BackButton" type="Button" parent="VBoxContainer/Header"]
layout_mode = 2
text = "← Back"

[node name="Title" type="Label" parent="VBoxContainer/Header"]
layout_mode = 2
size_flags_horizontal = 3
text = "Card Collection"
horizontal_alignment = 1

[node name="CollectionInfo" type="Label" parent="VBoxContainer/Header"]
layout_mode = 2
text = "Collection: 0 cards"

[node name="FilterContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="FactionLabel" type="Label" parent="VBoxContainer/FilterContainer"]
layout_mode = 2
text = "Faction:"

[node name="FactionFilter" type="OptionButton" parent="VBoxContainer/FilterContainer"]
layout_mode = 2

[node name="RarityLabel" type="Label" parent="VBoxContainer/FilterContainer"]
layout_mode = 2
text = "Rarity:"

[node name="RarityFilter" type="OptionButton" parent="VBoxContainer/FilterContainer"]
layout_mode = 2

[node name="SetLabel" type="Label" parent="VBoxContainer/FilterContainer"]
layout_mode = 2
text = "Set:"

[node name="SetFilter" type="OptionButton" parent="VBoxContainer/FilterContainer"]
layout_mode = 2

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="CardGridVBox" type="VBoxContainer" parent="VBoxContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="TopMargin" type="Control" parent="VBoxContainer/ScrollContainer/CardGridVBox"]
custom_minimum_size = Vector2(0, 32)
layout_mode = 2

[node name="CardGridCenter" type="CenterContainer" parent="VBoxContainer/ScrollContainer/CardGridVBox"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="CardGrid" type="GridContainer" parent="VBoxContainer/ScrollContainer/CardGridVBox/CardGridCenter"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/h_separation = 16
theme_override_constants/v_separation = 16
columns = 5

[node name="BottomMargin" type="Control" parent="VBoxContainer/ScrollContainer/CardGridVBox"]
custom_minimum_size = Vector2(0, 32)
layout_mode = 2

[node name="PreviewOverlay" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.7)

[node name="CardPreview" type="Control" parent="PreviewOverlay"]
custom_minimum_size = Vector2(320, 480)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -160.0
offset_top = -240.0
offset_right = 160.0
offset_bottom = 240.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="PreviewCard" type="Panel" parent="PreviewOverlay/CardPreview"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="CardArt" type="Panel" parent="PreviewOverlay/CardPreview/PreviewCard"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = -460.0
offset_right = 310.0
offset_bottom = -230.0
grow_vertical = 0
mouse_filter = 2

[node name="PowerNumbers" type="Control" parent="PreviewOverlay/CardPreview/PreviewCard/CardArt"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = -200.0
offset_bottom = -130.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="Top" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardArt/PowerNumbers"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -15.0
offset_top = 8.0
offset_right = 15.0
offset_bottom = 38.0
grow_horizontal = 2
horizontal_alignment = 1
vertical_alignment = 1

[node name="Right" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardArt/PowerNumbers"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -38.0
offset_top = -15.0
offset_right = -8.0
offset_bottom = 15.0
grow_horizontal = 0
grow_vertical = 2
horizontal_alignment = 1
vertical_alignment = 1

[node name="Bottom" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardArt/PowerNumbers"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -15.0
offset_top = -38.0
offset_right = 15.0
offset_bottom = -8.0
grow_horizontal = 2
grow_vertical = 0
horizontal_alignment = 1
vertical_alignment = 1

[node name="Left" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardArt/PowerNumbers"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 8.0
offset_top = -15.0
offset_right = 38.0
offset_bottom = 15.0
grow_vertical = 2
horizontal_alignment = 1
vertical_alignment = 1

[node name="CardInfo" type="VBoxContainer" parent="PreviewOverlay/CardPreview/PreviewCard"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = -220.0
offset_right = 310.0
offset_bottom = -10.0
grow_vertical = 0
mouse_filter = 2

[node name="NameContainer" type="HBoxContainer" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo"]
layout_mode = 2

[node name="NameLabel" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/NameContainer"]
layout_mode = 2
size_flags_horizontal = 0
text = "Name:"

[node name="CardName" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/NameContainer"]
layout_mode = 2
size_flags_horizontal = 3
autowrap_mode = 2

[node name="FactionContainer" type="HBoxContainer" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo"]
layout_mode = 2

[node name="FactionLabel" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/FactionContainer"]
layout_mode = 2
size_flags_horizontal = 0
text = "Faction:"

[node name="FactionName" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/FactionContainer"]
layout_mode = 2
size_flags_horizontal = 3
autowrap_mode = 2

[node name="RarityContainer" type="HBoxContainer" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo"]
layout_mode = 2

[node name="RarityLabel" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/RarityContainer"]
layout_mode = 2
size_flags_horizontal = 0
text = "Rarity:"

[node name="RarityName" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/RarityContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="AffinityContainer" type="HBoxContainer" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo"]
layout_mode = 2

[node name="AffinityLabel" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/AffinityContainer"]
layout_mode = 2
size_flags_horizontal = 0
text = "Affinity:"

[node name="AffinityName" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/AffinityContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="SetContainer" type="HBoxContainer" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo"]
layout_mode = 2

[node name="SetLabel" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/SetContainer"]
layout_mode = 2
size_flags_horizontal = 0
text = "Set:"

[node name="SetName" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/SetContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HSeparator2" type="HSeparator" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo"]
layout_mode = 2

[node name="StatsContainer" type="VBoxContainer" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo"]
layout_mode = 2

[node name="PowerSum" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/StatsContainer"]
layout_mode = 2

[node name="AvgPower" type="Label" parent="PreviewOverlay/CardPreview/PreviewCard/CardInfo/StatsContainer"]
layout_mode = 2

[node name="CloseButton" type="Button" parent="PreviewOverlay/CardPreview/PreviewCard"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -35.0
offset_top = 5.0
offset_right = -5.0
offset_bottom = 35.0
grow_horizontal = 0
text = "✕"
flat = true

[connection signal="pressed" from="VBoxContainer/Header/BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="PreviewOverlay/CardPreview/PreviewCard/CloseButton" to="." method="_on_close_button_pressed"]
