# Simple test script to verify FantasyTheme functionality
# This can be attached to any Control node for testing

extends Control

func _ready():
	print("Testing FantasyTheme system...")
	
	# Test color palette
	print("✓ Colors loaded: ", FantasyTheme.COLORS.size(), " colors available")
	
	# Test panel creation
	var test_panel = FantasyTheme.create_fantasy_panel("Legendary", true)
	print("✓ Fantasy panel created successfully")
	
	# Test button creation
	var test_button = FantasyTheme.create_fantasy_button("primary")
	print("✓ Fantasy button created successfully")
	
	# Test label settings
	var test_label = FantasyTheme.create_fantasy_label("title")
	print("✓ Fantasy label settings created successfully")
	
	# Test particle system (this will create actual particles)
	print("✓ Creating test particle system...")
	FantasyTheme.create_particle_system(self, "sparkles")
	
	print("🎨 FantasyTheme system test completed successfully!")
	print("✨ All fantasy components are working correctly!")