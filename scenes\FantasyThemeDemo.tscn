[gd_scene load_steps=2 format=3 uid="uid://wo54knwsrv3e"]

[ext_resource type="Script" uid="uid://dco0rcb1lrvce" path="res://scripts/FantasyThemeDemo.gd" id="1_demo"]

[node name="FantasyThemeDemo" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_demo")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.05, 0.05, 0.1, 1)

[node name="DemoContainer" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="DemoContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="DemoContainer/VBoxContainer"]
layout_mode = 2
text = "Fantasy Theme Demo"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="DemoContainer/VBoxContainer"]
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="DemoContainer/VBoxContainer"]
layout_mode = 2

[node name="CommonButton" type="Button" parent="DemoContainer/VBoxContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Common"

[node name="UncommonButton" type="Button" parent="DemoContainer/VBoxContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Uncommon"

[node name="RareButton" type="Button" parent="DemoContainer/VBoxContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Rare"

[node name="LegendaryButton" type="Button" parent="DemoContainer/VBoxContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Legendary"

[node name="HSeparator2" type="HSeparator" parent="DemoContainer/VBoxContainer"]
layout_mode = 2

[node name="PowerNumberDemo" type="HBoxContainer" parent="DemoContainer/VBoxContainer"]
layout_mode = 2

[node name="PowerLabel1" type="Label" parent="DemoContainer/VBoxContainer/PowerNumberDemo"]
layout_mode = 2
text = "5"

[node name="PowerLabel2" type="Label" parent="DemoContainer/VBoxContainer/PowerNumberDemo"]
layout_mode = 2
text = "7"

[node name="PowerLabel3" type="Label" parent="DemoContainer/VBoxContainer/PowerNumberDemo"]
layout_mode = 2
text = "3"

[node name="PowerLabel4" type="Label" parent="DemoContainer/VBoxContainer/PowerNumberDemo"]
layout_mode = 2
text = "9"

[node name="HSeparator3" type="HSeparator" parent="DemoContainer/VBoxContainer"]
layout_mode = 2

[node name="BackButton" type="Button" parent="DemoContainer/VBoxContainer"]
layout_mode = 2
text = "Back to Main Menu"
