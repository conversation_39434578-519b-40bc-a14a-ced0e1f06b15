[gd_scene load_steps=2 format=3 uid="uid://bk8n7qwxpkavx"]

[ext_resource type="Script" uid="uid://bunwr0vs827k5" path="res://scripts/PackOpening.gd" id="1_pack"]

[node name="PackOpening" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_pack")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.05, 0.05, 0.1, 1)

[node name="MainContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 40.0
offset_top = 40.0
offset_right = -40.0
offset_bottom = -40.0
grow_horizontal = 2
grow_vertical = 2

[node name="HeaderSection" type="HBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_vertical = 0

[node name="BackButton" type="Button" parent="MainContainer/HeaderSection"]
layout_mode = 2
text = "← BACK"
flat = true

[node name="TitleContainer" type="VBoxContainer" parent="MainContainer/HeaderSection"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TitleLabel" type="Label" parent="MainContainer/HeaderSection/TitleContainer"]
layout_mode = 2
text = "PACK OPENING"
horizontal_alignment = 1

[node name="PackInfoLabel" type="Label" parent="MainContainer/HeaderSection/TitleContainer"]
layout_mode = 2
text = "Pack Name Here"
horizontal_alignment = 1

[node name="Spacer" type="Control" parent="MainContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2

[node name="ContentSection" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="PackContainer" type="CenterContainer" parent="MainContainer/ContentSection"]
layout_mode = 2
size_flags_vertical = 3

[node name="PackDisplay" type="Panel" parent="MainContainer/ContentSection/PackContainer"]
custom_minimum_size = Vector2(300, 400)
layout_mode = 2

[node name="OpenButton" type="Button" parent="MainContainer/ContentSection"]
layout_mode = 2
text = "OPEN PACK"
flat = true

[node name="Spacer2" type="Control" parent="MainContainer/ContentSection"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="CardsContainer" type="HBoxContainer" parent="MainContainer/ContentSection"]
layout_mode = 2
alignment = 1

[connection signal="pressed" from="MainContainer/HeaderSection/BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="MainContainer/ContentSection/OpenButton" to="." method="_on_open_button_pressed"]
