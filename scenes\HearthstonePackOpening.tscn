[gd_scene load_steps=2 format=3 uid="uid://bujhxx2fsp1a8"]

[ext_resource type="Script" uid="uid://ddy35pljpnpn8" path="res://scripts/HearthstonePackOpening.gd" id="1_hearthstone"]

[node name="HearthstonePackOpening" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_hearthstone")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.05, 0.05, 0.1, 1)

[node name="MainContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="LeftSidebar" type="Panel" parent="MainContainer"]
custom_minimum_size = Vector2(300, 0)
layout_mode = 2

[node name="SidebarContainer" type="VBoxContainer" parent="MainContainer/LeftSidebar"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="HeaderContainer" type="HBoxContainer" parent="MainContainer/LeftSidebar/SidebarContainer"]
layout_mode = 2

[node name="BackButton" type="Button" parent="MainContainer/LeftSidebar/SidebarContainer/HeaderContainer"]
layout_mode = 2
text = "← BACK"
flat = true

[node name="Spacer" type="Control" parent="MainContainer/LeftSidebar/SidebarContainer/HeaderContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TitleLabel" type="Label" parent="MainContainer/LeftSidebar/SidebarContainer"]
layout_mode = 2
text = "PACK COLLECTION"
horizontal_alignment = 1

[node name="InstructionLabel" type="Label" parent="MainContainer/LeftSidebar/SidebarContainer"]
layout_mode = 2
text = "Drag a pack to the center to open it"
horizontal_alignment = 1
autowrap_mode = 2

[node name="PackScrollContainer" type="ScrollContainer" parent="MainContainer/LeftSidebar/SidebarContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="PackList" type="VBoxContainer" parent="MainContainer/LeftSidebar/SidebarContainer/PackScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="CenterArea" type="Control" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="DropZone" type="Panel" parent="MainContainer/CenterArea"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2

[node name="DropLabel" type="Label" parent="MainContainer/CenterArea/DropZone"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "Drop pack here to open"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CardsArea" type="Control" parent="MainContainer/CenterArea"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="CardsScroll" type="ScrollContainer" parent="MainContainer/CenterArea/CardsArea"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 32.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="CardsContainer" type="GridContainer" parent="MainContainer/CenterArea/CardsArea/CardsScroll"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/h_separation = 16
theme_override_constants/v_separation = 16
columns = 5

[connection signal="pressed" from="MainContainer/LeftSidebar/SidebarContainer/HeaderContainer/BackButton" to="." method="_on_back_button_pressed"]
