extends Control

@onready var background = $Background
@onready var title_label = $MainContainer/HeaderSection/TitleContainer/TitleLabel
@onready var pack_info_label = $MainContainer/HeaderSection/TitleContainer/PackInfoLabel
@onready var back_button = $MainContainer/HeaderSection/BackButton
@onready var pack_display = $MainContainer/ContentSection/PackContainer/PackDisplay
@onready var open_button = $MainContainer/ContentSection/OpenButton
@onready var cards_container = $MainContainer/ContentSection/CardsContainer

var current_pack: Dictionary = {}
var revealed_cards: Array[Card] = []

func _ready():
	setup_simple_theme()
	load_next_pack()

func setup_simple_theme():
	# Simple dark background
	background.color = Color(0.1, 0.1, 0.2, 1.0)
	
	# Style back button
	setup_button_style(back_button, Color(0.4, 0.4, 0.4))
	setup_button_style(open_button, Color(0.8, 0.6, 0.2))

func setup_button_style(button: Button, color: Color):
	var style_normal = StyleBoxFlat.new()
	style_normal.bg_color = color
	style_normal.corner_radius_top_left = 8
	style_normal.corner_radius_top_right = 8
	style_normal.corner_radius_bottom_left = 8
	style_normal.corner_radius_bottom_right = 8
	
	var style_hover = StyleBoxFlat.new()
	style_hover.bg_color = Color(color.r * 1.2, color.g * 1.2, color.b * 1.2, 1.0)
	style_hover.corner_radius_top_left = 8
	style_hover.corner_radius_top_right = 8
	style_hover.corner_radius_bottom_left = 8
	style_hover.corner_radius_bottom_right = 8
	
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)

func load_next_pack():
	var available_packs = SaveSystem.unopened_packs
	
	if available_packs.is_empty():
		# No packs available
		pack_info_label.text = "No packs available"
		open_button.disabled = true
		open_button.text = "NO PACKS"
		return
	
	# Show pack selection instead of just the first pack
	show_pack_selection()

func setup_pack_display():
	var pack_color = get_pack_color()
	
	var pack_style = StyleBoxFlat.new()
	pack_style.bg_color = Color(pack_color.r * 0.3, pack_color.g * 0.3, pack_color.b * 0.3, 0.9)
	pack_style.corner_radius_top_left = 20
	pack_style.corner_radius_top_right = 20
	pack_style.corner_radius_bottom_left = 20
	pack_style.corner_radius_bottom_right = 20
	pack_style.border_width_left = 4
	pack_style.border_width_right = 4
	pack_style.border_width_top = 4
	pack_style.border_width_bottom = 4
	pack_style.border_color = pack_color
	pack_style.shadow_color = Color(pack_color.r, pack_color.g, pack_color.b, 0.6)
	pack_style.shadow_size = 12
	pack_display.add_theme_stylebox_override("panel", pack_style)
	
	# Add pack icon
	var pack_icon = Label.new()
	pack_icon.text = "📦"
	pack_icon.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	pack_icon.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	pack_icon.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	pack_icon.add_theme_font_size_override("font_size", 120)
	pack_icon.modulate = pack_color
	pack_display.add_child(pack_icon)

func get_pack_color() -> Color:
	if current_pack.has("color"):
		var color_value = current_pack.color
		# Handle different color formats
		if color_value is Color:
			return color_value
		elif color_value is Array and color_value.size() >= 3:
			return Color(color_value[0], color_value[1], color_value[2], color_value.get(3, 1.0))
		elif color_value is Dictionary:
			return Color(color_value.get("r", 0.7), color_value.get("g", 0.7), color_value.get("b", 0.7), color_value.get("a", 1.0))
	
	# Default colors based on pack name/type
	if "Legendary" in current_pack.name or "Dragon" in current_pack.name:
		return Color(0.9, 0.2, 0.9)  # Purple
	elif "Rare" in current_pack.name or "Mystic" in current_pack.name:
		return Color(1.0, 0.8, 0.0)  # Gold
	else:
		return Color(0.7, 0.7, 0.7)  # Silver

func _on_open_button_pressed():
	if current_pack.is_empty():
		return
	
	# Remove pack from inventory at the specific index if it was selected
	if current_pack.has("_inventory_index"):
		SaveSystem.unopened_packs.remove_at(current_pack["_inventory_index"])
		SaveSystem.save_game()
	else:
		# Fallback: remove first pack if no index stored
		SaveSystem.open_pack()
	
	# Generate cards from the pack
	generate_pack_cards()
	
	# Animate pack opening
	animate_pack_opening()

func generate_pack_cards():
	revealed_cards.clear()
	var all_cards = CardManager.get_all_cards()
	
	# Generate cards based on pack guarantees
	var cards_to_generate = current_pack.cards
	var guaranteed_rare = current_pack.get("guaranteed_rare", false)
	var guaranteed_legendary = current_pack.get("guaranteed_legendary", false)
	
	# Add guaranteed cards first
	if guaranteed_legendary:
		var legendary_cards = CardManager.get_cards_by_rarity("Legendary")
		if legendary_cards.size() > 0:
			revealed_cards.append(legendary_cards[randi() % legendary_cards.size()])
			cards_to_generate -= 1
	elif guaranteed_rare:
		var rare_cards = CardManager.get_cards_by_rarity("Rare")
		if rare_cards.size() > 0:
			revealed_cards.append(rare_cards[randi() % rare_cards.size()])
			cards_to_generate -= 1
	
	# Fill remaining slots with random cards
	for i in range(cards_to_generate):
		var random_card = all_cards[randi() % all_cards.size()]
		revealed_cards.append(random_card)
	
	# Unlock all revealed cards
	for card in revealed_cards:
		SaveSystem.unlock_card(card.name)

func animate_pack_opening():
	# Disable open button
	open_button.disabled = true
	open_button.text = "OPENING..."
	
	# Animate pack disappearing
	var pack_tween = create_tween()
	pack_tween.tween_property(pack_display, "scale", Vector2(1.2, 1.2), 0.3)
	pack_tween.tween_property(pack_display, "modulate:a", 0.0, 0.5)
	
	await pack_tween.finished
	
	# Show revealed cards
	show_revealed_cards()

func show_revealed_cards():
	# Create overlay for cards to appear on top
	var overlay = Control.new()
	overlay.name = "CardOverlay"
	overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	overlay.mouse_filter = Control.MOUSE_FILTER_STOP
	add_child(overlay)
	
	# Semi-transparent background
	var bg = ColorRect.new()
	bg.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	bg.color = Color(0, 0, 0, 0.7)
	overlay.add_child(bg)
	
	# Create centered container for cards
	var center_container = CenterContainer.new()
	center_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	overlay.add_child(center_container)
	
	var main_container = VBoxContainer.new()
	main_container.add_theme_constant_override("separation", 20)
	main_container.custom_minimum_size = Vector2(800, 600)  # Set reasonable size
	center_container.add_child(main_container)
	
	# Title for the pack results
	var title_label = Label.new()
	title_label.text = "Pack Contents: " + current_pack.name
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 24)
	title_label.add_theme_color_override("font_color", Color.WHITE)
	main_container.add_child(title_label)
	
	# Create scroll container for cards
	var scroll_container = ScrollContainer.new()
	scroll_container.custom_minimum_size = Vector2(800, 400)
	scroll_container.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	scroll_container.size_flags_vertical = Control.SIZE_EXPAND_FILL
	main_container.add_child(scroll_container)
	
	# Create grid container for cards inside scroll container
	var card_grid = GridContainer.new()
	card_grid.columns = min(4, revealed_cards.size())  # Max 4 columns, or fewer if less cards
	card_grid.add_theme_constant_override("h_separation", 15)
	card_grid.add_theme_constant_override("v_separation", 15)
	card_grid.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	scroll_container.add_child(card_grid)
	
	# Create all card displays first
	var card_displays = []
	for i in range(revealed_cards.size()):
		var card = revealed_cards[i]
		
		# Use actual CardDisplay scene for proper art rendering
		var card_display_scene = preload("res://scenes/CardDisplay.tscn")
		var card_display = card_display_scene.instantiate()
		
		# Temporarily unlock card for display
		var was_locked = not SaveSystem.is_card_unlocked(card.name)
		if was_locked:
			SaveSystem.unlock_card(card.name)
		
		card_display.modulate.a = 0
		
		# Don't override custom_minimum_size - let CardDisplay use its natural 160x160 size
		# The GridContainer will handle the layout properly
		
		# Completely disable all mouse interactions to prevent hover effects
		card_display.mouse_filter = Control.MOUSE_FILTER_IGNORE
		
		card_grid.add_child(card_display)
		card_displays.append(card_display)
	
	# Wait one frame for all cards to be added to the scene tree
	await get_tree().process_frame
	
	# Setup all cards
	for i in range(card_displays.size()):
		var card_display = card_displays[i]
		var card = revealed_cards[i]
		card_display.setup_card(card)
		
		# Don't scale the cards - let GridContainer handle layout with natural size
		# The 3-column grid with 160x160 cards should fit properly
		
		# Disable all mouse interactions recursively
		disable_mouse_recursively(card_display)
	
	# Wait another frame for all cards to be properly initialized
	await get_tree().process_frame
	
	# Force layout update
	card_grid.queue_redraw()
	
	# Now animate all cards
	for i in range(card_displays.size()):
		var card_display = card_displays[i]
		var card_tween = create_tween()
		card_tween.tween_property(card_display, "modulate:a", 1.0, 0.5).set_delay(i * 0.2)
		# Remove scale animations that might interfere with layout
		# card_tween.tween_property(card_display, "scale", Vector2(0.8, 0.8), 0.2).set_delay(i * 0.2)
		# card_tween.tween_property(card_display, "scale", Vector2(0.7, 0.7), 0.2).set_delay(i * 0.2 + 0.2)
	
	# Add close button
	var close_button = Button.new()
	close_button.text = "CLOSE"
	close_button.custom_minimum_size = Vector2(200, 50)
	close_button.flat = true
	setup_button_style(close_button, Color(0.6, 0.2, 0.2))
	main_container.add_child(close_button)
	
	close_button.pressed.connect(_on_close_pack_results.bind(overlay))
	
	# Hide the main open button while overlay is shown
	open_button.visible = false

func create_simple_card_display(card: Card) -> Panel:
	var card_panel = Panel.new()
	card_panel.custom_minimum_size = Vector2(120, 160)
	
	# Style based on rarity
	var rarity_color = get_rarity_color(card.rarity)
	var card_style = StyleBoxFlat.new()
	card_style.bg_color = Color(rarity_color.r * 0.3, rarity_color.g * 0.3, rarity_color.b * 0.3, 0.9)
	card_style.corner_radius_top_left = 8
	card_style.corner_radius_top_right = 8
	card_style.corner_radius_bottom_left = 8
	card_style.corner_radius_bottom_right = 8
	card_style.border_width_left = 2
	card_style.border_width_right = 2
	card_style.border_width_top = 2
	card_style.border_width_bottom = 2
	card_style.border_color = rarity_color
	card_panel.add_theme_stylebox_override("panel", card_style)
	
	# Card name
	var name_label = Label.new()
	name_label.text = card.name
	name_label.position = Vector2(5, 5)
	name_label.size = Vector2(110, 40)
	name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	name_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	name_label.add_theme_font_size_override("font_size", 10)
	name_label.add_theme_color_override("font_color", Color.WHITE)
	name_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	card_panel.add_child(name_label)
	
	# Rarity label
	var rarity_label = Label.new()
	rarity_label.text = card.rarity
	rarity_label.position = Vector2(5, 140)
	rarity_label.size = Vector2(110, 15)
	rarity_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	rarity_label.add_theme_font_size_override("font_size", 9)
	rarity_label.add_theme_color_override("font_color", rarity_color)
	card_panel.add_child(rarity_label)
	
	return card_panel

func get_rarity_color(rarity: String) -> Color:
	match rarity:
		"Common":
			return Color(0.7, 0.7, 0.7)
		"Uncommon":
			return Color(0.2, 0.8, 0.3)
		"Rare":
			return Color(1.0, 0.8, 0.0)
		"Legendary":
			return Color(0.9, 0.2, 0.9)
		_:
			return Color.WHITE

func _on_close_pack_results(overlay: Control):
	# Remove the overlay
	overlay.queue_free()
	
	# Reset pack display and show open button
	pack_display.modulate.a = 1.0
	pack_display.scale = Vector2(1.0, 1.0)
	open_button.visible = true
	open_button.disabled = false
	
	# Update button text based on remaining packs
	if SaveSystem.get_pack_count() > 0:
		open_button.text = "OPEN PACK"
		load_next_pack()
	else:
		open_button.text = "NO PACKS"
		open_button.disabled = true

func show_pack_selection():
	# Clear existing pack display
	for child in pack_display.get_children():
		child.queue_free()
	
	var available_packs = SaveSystem.unopened_packs
	
	if available_packs.size() == 1:
		# Only one pack, select it automatically
		current_pack = available_packs[0]
		pack_info_label.text = current_pack.name + " - " + str(current_pack.cards) + " cards"
		setup_pack_display()
		open_button.disabled = false
		open_button.text = "OPEN PACK"
		return
	
	# Multiple packs - show selection
	pack_info_label.text = "Choose a pack to open (" + str(available_packs.size()) + " available)"
	open_button.disabled = true
	open_button.text = "SELECT A PACK"
	
	# Create pack selection grid
	var pack_grid = GridContainer.new()
	pack_grid.columns = 3
	pack_grid.add_theme_constant_override("h_separation", 10)
	pack_grid.add_theme_constant_override("v_separation", 10)
	pack_grid.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	pack_display.add_theme_stylebox_override("panel", StyleBoxFlat.new())  # Clear background
	pack_display.add_child(pack_grid)
	
	# Create selectable pack buttons
	for i in range(available_packs.size()):
		var pack = available_packs[i]
		var pack_button = create_pack_selection_button(pack, i)
		pack_grid.add_child(pack_button)

func create_pack_selection_button(pack: Dictionary, index: int) -> Button:
	var pack_btn = Button.new()
	pack_btn.custom_minimum_size = Vector2(80, 100)
	pack_btn.flat = true
	
	# Get pack color
	var pack_color = get_pack_color_for_pack(pack)
	
	# Style the pack button
	var btn_style = StyleBoxFlat.new()
	btn_style.bg_color = Color(pack_color.r * 0.3, pack_color.g * 0.3, pack_color.b * 0.3, 0.9)
	btn_style.corner_radius_top_left = 12
	btn_style.corner_radius_top_right = 12
	btn_style.corner_radius_bottom_left = 12
	btn_style.corner_radius_bottom_right = 12
	btn_style.border_width_left = 2
	btn_style.border_width_right = 2
	btn_style.border_width_top = 2
	btn_style.border_width_bottom = 2
	btn_style.border_color = pack_color
	
	var btn_style_hover = StyleBoxFlat.new()
	btn_style_hover.bg_color = Color(pack_color.r * 0.5, pack_color.g * 0.5, pack_color.b * 0.5, 1.0)
	btn_style_hover.corner_radius_top_left = 12
	btn_style_hover.corner_radius_top_right = 12
	btn_style_hover.corner_radius_bottom_left = 12
	btn_style_hover.corner_radius_bottom_right = 12
	btn_style_hover.border_width_left = 3
	btn_style_hover.border_width_right = 3
	btn_style_hover.border_width_top = 3
	btn_style_hover.border_width_bottom = 3
	btn_style_hover.border_color = Color(pack_color.r * 1.2, pack_color.g * 1.2, pack_color.b * 1.2)
	btn_style_hover.shadow_color = Color(pack_color.r, pack_color.g, pack_color.b, 0.6)
	btn_style_hover.shadow_size = 8
	
	pack_btn.add_theme_stylebox_override("normal", btn_style)
	pack_btn.add_theme_stylebox_override("hover", btn_style_hover)
	
	# Create pack content
	var pack_container = VBoxContainer.new()
	pack_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	pack_container.add_theme_constant_override("separation", 5)
	pack_btn.add_child(pack_container)
	
	# Pack icon
	var pack_icon = Label.new()
	pack_icon.text = "📦"
	pack_icon.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	pack_icon.add_theme_font_size_override("font_size", 32)
	pack_icon.modulate = pack_color
	pack_container.add_child(pack_icon)
	
	# Pack name
	var pack_name = Label.new()
	pack_name.text = pack.name
	pack_name.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	pack_name.add_theme_font_size_override("font_size", 10)
	pack_name.add_theme_color_override("font_color", Color.WHITE)
	pack_name.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	pack_container.add_child(pack_name)
	
	# Pack info
	var pack_info = Label.new()
	pack_info.text = str(pack.cards) + " cards"
	pack_info.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	pack_info.add_theme_font_size_override("font_size", 9)
	pack_info.add_theme_color_override("font_color", Color(0.8, 0.8, 0.8))
	pack_container.add_child(pack_info)
	
	# Connect selection
	pack_btn.pressed.connect(_on_pack_selected.bind(index))
	
	return pack_btn

func get_pack_color_for_pack(pack: Dictionary) -> Color:
	if pack.has("color"):
		return pack.color
	
	# Default colors based on pack name/type
	if "Legendary" in pack.name or "Dragon" in pack.name:
		return Color(0.9, 0.2, 0.9)  # Purple
	elif "Rare" in pack.name or "Mystic" in pack.name:
		return Color(1.0, 0.8, 0.0)  # Gold
	else:
		return Color(0.7, 0.7, 0.7)  # Silver

func _on_pack_selected(pack_index: int):
	# Set the selected pack as current (but don't remove from inventory yet)
	current_pack = SaveSystem.unopened_packs[pack_index].duplicate()
	
	# Store the pack index for later removal when actually opened
	current_pack["_inventory_index"] = pack_index
	
	# Clear pack display and show the selected pack
	for child in pack_display.get_children():
		child.queue_free()
	
	# Update UI
	pack_info_label.text = current_pack.name + " - " + str(current_pack.cards) + " cards"
	setup_pack_display()
	open_button.disabled = false
	open_button.text = "OPEN PACK"
	
	# Add back to selection button if there are multiple packs
	if SaveSystem.unopened_packs.size() > 1:
		add_back_to_selection_button()

func add_back_to_selection_button():
	# Add a small back button to return to pack selection
	var back_to_selection = Button.new()
	back_to_selection.text = "← Back to Selection"
	back_to_selection.size = Vector2(150, 30)
	back_to_selection.position = Vector2(10, 10)
	back_to_selection.flat = true
	setup_button_style(back_to_selection, Color(0.3, 0.3, 0.4))
	
	pack_display.add_child(back_to_selection)
	back_to_selection.pressed.connect(_on_back_to_selection_pressed)

func _on_back_to_selection_pressed():
	# Return to pack selection without removing the pack from inventory
	current_pack.clear()
	load_next_pack()

func disable_mouse_recursively(node: Node):
	# Recursively disable mouse interactions on all Control nodes
	if node is Control:
		node.mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	for child in node.get_children():
		disable_mouse_recursively(child)

func _on_back_button_pressed():
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.3)
	await tween.finished
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")
