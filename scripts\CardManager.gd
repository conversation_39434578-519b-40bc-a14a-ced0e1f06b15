extends Node

var all_cards: Array[Card] = []

func _ready():
	load_cards_from_database()

func load_cards_from_database():
	var file = FileAccess.open("res://data/cards_database.json", FileAccess.READ)
	if file == null:
		print("Error: Could not open cards database file")
		return
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		print("Error parsing JSON: ", json.get_error_message())
		return
	
	var data = json.data
	if not data.has("cards"):
		print("Error: No 'cards' key found in JSON")
		return
	
	all_cards.clear()
	for card_data in data.cards:
		var card = Card.new(card_data)
		all_cards.append(card)
	
	print("Loaded ", all_cards.size(), " cards from database")

func get_all_cards() -> Array[Card]:
	return all_cards

func get_cards_by_faction(faction_name: String) -> Array[Card]:
	var faction_cards: Array[Card] = []
	for card in all_cards:
		if card.faction == faction_name:
			faction_cards.append(card)
	return faction_cards

func get_cards_by_rarity(rarity_name: String) -> Array[Card]:
	var rarity_cards: Array[Card] = []
	for card in all_cards:
		if card.rarity == rarity_name:
			rarity_cards.append(card)
	return rarity_cards

func unlock_card(card_name: String) -> bool:
	return SaveSystem.unlock_card(card_name)

func is_card_unlocked(card_name: String) -> bool:
	return SaveSystem.is_card_unlocked(card_name)

func get_card_by_name(card_name: String) -> Card:
	for card in all_cards:
		if card.name == card_name:
			return card
	return null

func get_available_factions() -> Array[String]:
	var factions: Array[String] = []
	for card in all_cards:
		if not factions.has(card.faction):
			factions.append(card.faction)
	return factions

func get_available_sets() -> Array[String]:
	var sets: Array[String] = []
	for card in all_cards:
		if not sets.has(card.card_set):
			sets.append(card.card_set)
	return sets
