[gd_scene load_steps=2 format=3 uid="uid://bk8n7qwxpkavx"]

[ext_resource type="Script" uid="uid://1uifrkwyqb4n" path="res://scripts/Shop.gd" id="1_shop"]

[node name="Shop" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_shop")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.05, 0.05, 0.1, 1)

[node name="MainContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 40.0
offset_top = 40.0
offset_right = -40.0
offset_bottom = -40.0
grow_horizontal = 2
grow_vertical = 2

[node name="HeaderSection" type="HBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_vertical = 0

[node name="BackButton" type="Button" parent="MainContainer/HeaderSection"]
layout_mode = 2
text = "← BACK"
flat = true

[node name="TitleContainer" type="VBoxContainer" parent="MainContainer/HeaderSection"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TitleLabel" type="Label" parent="MainContainer/HeaderSection/TitleContainer"]
layout_mode = 2
text = "MYSTICAL SHOP"
horizontal_alignment = 1

[node name="GoldContainer" type="HBoxContainer" parent="MainContainer/HeaderSection"]
layout_mode = 2
alignment = 2

[node name="GoldLabel" type="Label" parent="MainContainer/HeaderSection/GoldContainer"]
layout_mode = 2
text = "💰 1000 Gold"

[node name="Spacer" type="Control" parent="MainContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2

[node name="ContentSection" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="SectionTitle" type="Label" parent="MainContainer/ContentSection"]
layout_mode = 2
text = "Card Packs"
horizontal_alignment = 1

[node name="ScrollContainer" type="ScrollContainer" parent="MainContainer/ContentSection"]
layout_mode = 2
size_flags_vertical = 3

[node name="PackContainer" type="HBoxContainer" parent="MainContainer/ContentSection/ScrollContainer"]
layout_mode = 2
alignment = 1

[node name="PurchasePopup" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="PopupBackground" type="Panel" parent="PurchasePopup"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="PurchasePopup/PopupBackground"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="PopupTitle" type="Label" parent="PurchasePopup/PopupBackground/VBoxContainer"]
layout_mode = 2
text = "Purchase Pack?"
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="PurchasePopup/PopupBackground/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="PopupDescription" type="Label" parent="PurchasePopup/PopupBackground/VBoxContainer"]
layout_mode = 2
text = "Pack description here"
horizontal_alignment = 1
autowrap_mode = 3

[node name="Spacer2" type="Control" parent="PurchasePopup/PopupBackground/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="PopupPrice" type="Label" parent="PurchasePopup/PopupBackground/VBoxContainer"]
layout_mode = 2
text = "Cost: 💰 100 Gold"
horizontal_alignment = 1

[node name="Spacer3" type="Control" parent="PurchasePopup/PopupBackground/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ButtonContainer" type="HBoxContainer" parent="PurchasePopup/PopupBackground/VBoxContainer"]
layout_mode = 2

[node name="ConfirmButton" type="Button" parent="PurchasePopup/PopupBackground/VBoxContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "CONFIRM"
flat = true

[node name="CancelButton" type="Button" parent="PurchasePopup/PopupBackground/VBoxContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "CANCEL"
flat = true

[connection signal="pressed" from="MainContainer/HeaderSection/BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="PurchasePopup/PopupBackground/VBoxContainer/ButtonContainer/ConfirmButton" to="." method="_on_confirm_button_pressed"]
[connection signal="pressed" from="PurchasePopup/PopupBackground/VBoxContainer/ButtonContainer/CancelButton" to="." method="_on_cancel_button_pressed"]
